'use client'

import DepartmentList from '@/app/ui/department/list'
import { useEffect, useState } from 'react'
import { preventCrewAccess } from '../helpers/userHelper'
import Loading from '../loading'
import { Button, ListHeader } from '@/components/ui'
import Link from 'next/link'

const DepartmentPage = () => {
    const [useDepartment, setUseDepartment] = useState<any>(null)
    useEffect(() => {
        preventCrewAccess()
        if (localStorage.getItem('useDepartment') === 'true') {
            setUseDepartment(true)
        } else {
            setUseDepartment(false)
        }
    }, [])
    return (
        <>
            {useDepartment ? (
                <>
                    <ListHeader
                        title="Departments"
                        actions={
                            <Link href={'/department/create'}>
                                <Button>Add Department</Button>
                            </Link>
                        }
                    />
                    <div className="mt-16">
                        <DepartmentList />
                    </div>
                </>
            ) : (
                <>
                    {useDepartment === null ? (
                        <Loading />
                    ) : (
                        <Loading message="Departments are not enabled, please enable the departments from settings to use departments." />
                    )}
                </>
            )}
        </>
    )
}

export default DepartmentPage
