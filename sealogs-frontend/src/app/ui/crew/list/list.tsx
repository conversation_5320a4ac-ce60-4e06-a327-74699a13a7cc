'use client'

import { useEffect, useState } from 'react'
import { useLazyQuery, useMutation } from '@apollo/client'
import Link from 'next/link'
import { UPDATE_USER } from '@/app/lib/graphQL/mutation'
import {
    getCrewDuties,
    GetCrewListWithTrainingStatus,
    getVesselBriefList,
} from '@/app/lib/actions'

import { isEmpty } from 'lodash'
import { useRouter } from 'next/navigation'
import { useSidebar } from '@/components/ui/sidebar'
import { Button } from '@/components/ui/button'
import {
    createColumns,
    DataTable,
    ExtendedColumnDef,
} from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { Avatar, AvatarFallback, Badge, getCrewInitials } from '@/components/ui'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { CircleAlert, Check, Laptop } from 'lucide-react'
import { SealogsCrewIcon } from '@/app/lib/icons/SealogsCrewIcon'
import { CrewFilterActions } from '@/components/filter/components/crew-actions'
import { ListHeader } from '@/components/ui/list-header'
import VesselIcon from '../../vessels/vesel-icon'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'
import Loading from '@/app/loading'
import { ReadDepartments, ReadSeaLogsMembers } from './queries'
import VesselDisplay from '../components/vessel-display'

export default function CrewList(props: any) {
    const router = useRouter()
    const [crewList, setCrewList] = useState<any>([])
    const [vessels, setVessels] = useState<
        Array<{ label: string; value: number }>
    >([])

    const [showActiveUsers, setShowActiveUsers] = useState(true)
    const { isMobile } = useSidebar()
    // const [isLoading, setIsLoading] = useState(true)
    const [departments, setDepartments] = useState<any>([])
    const [duties, setDuties] = useState<any>([])
    const limit = 100
    const [pageInfo, setPageInfo] = useState({
        totalCount: 0,
        hasNextPage: false,
        hasPreviousPage: false,
    })
    const [page, setPage] = useState(0)
    let [filter, setFilter] = useState({
        isArchived: { eq: false },
    } as SearchFilter)
    const [trainingStatusFilter, setTrainingStatusFilter] = useState<
        string | null
    >(null)

    // Update crew duties based on active/archived state.
    const handleSetCrewDuties = (crewDuties: any) => {
        const activeDuties = crewDuties.filter((duty: any) =>
            showActiveUsers ? !duty.archived : duty.archived,
        )
        const formattedCrewDuties = activeDuties.map((duty: any) => {
            return {
                label: duty.title,
                value: duty.id,
            }
        })
        setDuties(formattedCrewDuties)
    }
    getCrewDuties(handleSetCrewDuties)

    // Render departments recursively.
    const renderDepartment = (
        departments: any[],
        parentID: number = 0,
        depth: number = 0,
    ): any[] => {
        return departments
            .filter((department: any) => +department.parentID === parentID)
            .flatMap((department: any) => {
                const children = renderDepartment(
                    departments,
                    +department.id,
                    depth + 1,
                )
                const item = {
                    ...department,
                    level: depth,
                }
                return [item, ...children]
            })
    }
    /* const [readDepartments, { loading: readDepartmentsLoading }] = useLazyQuery(
        ReadDepartments,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readDepartments.nodes
                if (data) {
                    const formattedData = renderDepartment(data)
                    setDepartments(formattedData)
                }
            },
            onError: (error: any) => {
                console.error('queryCrewMembers error', error)
            },
        },
    ) */
    /* const loadDepartments = async () => {
        await readDepartments()
    } */
    /* useEffect(() => {
        if (isLoading) {
            loadDepartments()
            setIsLoading(false)
        }
    }, [isLoading]) */

    // Set vessels from vessel brief list.
    const handleSetVessels = (vessels: any) => {
        const vesselSelection = vessels.map((vessel: any) => {
            return { label: vessel.title, value: vessel.id }
        })
        setVessels(vesselSelection)
        //loadCrewMembers()
    }
    getVesselBriefList(handleSetVessels)

    const [queryCrewMembers, { loading: queryCrewMembersLoading }] =
        useLazyQuery(ReadSeaLogsMembers, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                handleSetCrewMembers(response.readSeaLogsMembers.nodes)
                setPageInfo(response.readSeaLogsMembers.pageInfo)
                return response.readSeaLogsMembers.nodes
            },
            onError: (error: any) => {
                console.error('queryCrewMembers error', error)
            },
        })
    const handleSetCrewMembers = (crewMembers: any) => {
        const transformedCrewList = GetCrewListWithTrainingStatus(
            crewMembers,
            vessels,
        )
        setCrewList(transformedCrewList)
    }

    // Function to filter crew list by training status
    const filterCrewByTrainingStatus = (
        crewList: any[],
        statusFilter: string | null,
    ) => {
        const crews = [...crewList].map((crew: any) => {
            const dues = crew.trainingStatus.dues
            if (dues.length === 0) {
                return { ...crew, trainingStatus: { label: 'Good', dues: [] } }
            } else if (dues.some((due: any) => due.status.isOverdue)) {
                return {
                    ...crew,
                    trainingStatus: {
                        label: 'Overdue',
                        dues: dues.filter((due: any) => due.status.isOverdue),
                    },
                }
            } else {
                return {
                    ...crew,
                    trainingStatus: {
                        label: ' ',
                        dues: dues.filter((due: any) => !due.status.isOverdue),
                    },
                }
            }
        })
        if (!statusFilter) return crews
        return crews.filter((crew: any) => {
            const trainingStatus = crew.trainingStatus?.label
            const dues = crew.trainingStatus?.dues || []

            if (statusFilter === 'Good') {
                return trainingStatus === 'Good'
            } else if (statusFilter === 'Overdue') {
                return trainingStatus === 'Overdue'
            } else if (statusFilter === 'Due Soon') {
                // Due Soon is represented by an empty string label with dues
                // This happens when there are training sessions due within 7 days but not overdue
                return trainingStatus === ' ' && dues.length > 0
            }

            return true
        })
    }

    // Get filtered crew list for display
    const filteredCrewList = filterCrewByTrainingStatus(
        crewList,
        trainingStatusFilter,
    )

    const loadCrewMembers = async (
        startPage: number = 0,
        searchFilter: any = { ...filter },
    ) => {
        /*searchFilter.isArchived = { eq: !showActiveUsers }
        const updatedFilter: SearchFilter = {
            ...searchFilter,
            isArchived: { eq: !showActiveUsers },
        }*/
        await queryCrewMembers({
            variables: {
                limit: limit,
                offset: startPage * limit,
                filter: searchFilter,
            },
        })
    }

    const [mutationUpdateUser] = useMutation(UPDATE_USER, {
        onCompleted: () => {},
        onError: (error: any) => {
            console.error('mutationUpdateUser error', error)
        },
    })

    const handleCrewDuty = async (duty: any, user: any) => {
        const selectedUser = {
            ...crewList.find((crew: any) => crew.ID === user.ID),
        }
        const newPrimaryDutyID = duty.value
        if (selectedUser) {
            const updatedCrewList = crewList.map((crew: any) => {
                if (crew.ID === user.ID) {
                    return {
                        ...crew,
                        PrimaryDutyID: newPrimaryDutyID,
                    }
                }
                return crew
            })
            setCrewList(updatedCrewList)
            // Update user
            const variables = {
                input: {
                    id: +user.id,
                    primaryDutyID: newPrimaryDutyID,
                },
            }
            await mutationUpdateUser({ variables })
        }
    }

    const handleNavigationClick = (newPage: any) => {
        if (newPage < 0 || newPage === page) return
        setPage(newPage)
        loadCrewMembers(newPage)
    }

    const handleFilterOnChange = ({ type, data }: any) => {
        interface SearchFilter {
            isArchived?: { eq: boolean }
            vehicles?: { id: { contains?: number; in?: number[] } }
            primaryDutyID?: { eq?: number; in?: number[] }
            q?: { contains: string }
        }

        const searchFilter: SearchFilter = { ...filter }

        // Handle training status filter separately since it's client-side
        if (type === 'trainingStatus') {
            if (data && data.value) {
                setTrainingStatusFilter(data.value)
            } else {
                setTrainingStatusFilter(null)
            }
            return // Don't reload crew members for client-side filter
        }

        if (type === 'vessel') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.vehicles = {
                    id: { in: data.map((item) => +item.value) },
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.vehicles = { id: { contains: +data.value } }
            } else {
                delete searchFilter.vehicles
            }
        }
        if (type === 'crewDuty') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.primaryDutyID = {
                    in: data.map((item) => +item.value),
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.primaryDutyID = { eq: +data.value }
            } else {
                delete searchFilter.primaryDutyID
            }
        }
        if (type === 'keyword') {
            if (!isEmpty(data.value)) {
                searchFilter.q = { contains: data.value }
            } else {
                delete searchFilter.q
            }
        }
        if (type === 'isArchived') {
            if (data !== undefined) {
                searchFilter.isArchived = { eq: !data }
            } else {
                delete searchFilter.isArchived
            }
        }
        setFilter(searchFilter)
        //setPage(0)
        loadCrewMembers(0, searchFilter)
    }

    useEffect(() => {
        setPage(0)
        loadCrewMembers(0, filter)
    }, [showActiveUsers, filter])

    // Column definitions for the DataTable.
    const columns: ExtendedColumnDef<any, unknown>[] = [
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Name" />
            ),
            cellClassName: 'phablet:w-auto w-full',
            cell: ({ row }: { row: any }) => {
                const crewMember: any = row.original
                const fullName = `${crewMember.firstName} ${crewMember.surname}`

                return (
                    <div className="w-full py-2.5 space-y-1.5">
                        <div className="flex justify-between items-center">
                            <div className="flex-1 flex justify-start items-center gap-2.5">
                                <Avatar
                                    size="sm"
                                    variant={
                                        crewMember.trainingStatus?.label ===
                                        'Overdue'
                                            ? 'destructive'
                                            : 'success'
                                    }>
                                    <AvatarFallback>
                                        {getCrewInitials(
                                            crewMember.firstName,
                                            crewMember.surname,
                                        )}
                                    </AvatarFallback>
                                </Avatar>
                                <div className="grid min-w-32">
                                    <Link
                                        href={`/crew/info?id=${crewMember.id}`}
                                        className="items-center truncate text-nowrap">
                                        {fullName || '--'}
                                    </Link>
                                    <div className="text-curious-blue-400 uppercase laptop:hidden text-[10px]">
                                        {crewMember.primaryDuty.title}
                                    </div>
                                </div>
                            </div>
                            <div className="phablet:hidden">
                                {crewMember.trainingStatus.label ===
                                'Overdue' ? (
                                    <Badge variant="destructive" type="circle">
                                        {crewMember.trainingStatus.dues.length}
                                    </Badge>
                                ) : (
                                    <Badge variant="success" type="circle">
                                        <Check className="h-5 w-5" />
                                    </Badge>
                                )}
                            </div>
                        </div>

                        {crewMember.vehicles.nodes.length > 0 && (
                            <div className="tablet-lg:hidden">
                                <VesselDisplay
                                    vessels={crewMember.vehicles.nodes}
                                    maxVisibleVessels={3}
                                />
                            </div>
                        )}
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const fullNameA =
                    `${rowA?.original?.firstName} ${rowA?.original?.surname}` ||
                    ''
                const fullNameB =
                    `${rowB?.original?.firstName} ${rowB?.original?.surname}` ||
                    ''
                return fullNameA.localeCompare(fullNameB)
            },
        },
        {
            accessorKey: 'vehicles',
            cellAlignment: 'left',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Vessel" />
            ),
            breakpoint: 'tablet-lg',
            cell: ({ row }: { row: any }) => {
                const crew = row.original
                return (
                    <VesselDisplay
                        vessels={crew.vehicles.nodes}
                        maxVisibleVessels={4}
                    />
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const titleA = rowA?.original?.vehicles?.nodes?.[0]?.title || ''
                const titleB = rowB?.original?.vehicles?.nodes?.[0]?.title || ''

                return titleA.localeCompare(titleB)
            },
        },
        {
            accessorKey: 'primaryDuty',
            cellAlignment: 'right',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Primary duty" />
            ),
            breakpoint: 'laptop',
            cell: ({ row }: { row: any }) => {
                const crew = row.original
                return (
                    <div className="whitespace-normal px-5">
                        {crew.primaryDuty.title}
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.primaryDuty?.title || ''
                const valueB = rowB?.original?.primaryDuty?.title || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'trainingStatus',
            header: ({ column }: { column: any }) => (
                <div className="flex justify-center w-full">
                    <DataTableSortHeader column={column} title="Training" />
                </div>
            ),
            breakpoint: 'phablet',
            cell: ({ row }: { row: any }) => {
                const crew = row.original
                return (
                    <div className="flex justify-center w-full">
                        {crew.trainingStatus.label === 'Overdue' ? (
                            <Badge variant="destructive" type="circle">
                                {crew.trainingStatus.dues.length}
                            </Badge>
                        ) : (
                            <Badge variant="success" type="circle">
                                <Check className="h-5 w-5" />
                            </Badge>
                        )}
                    </div>
                )
            },
        },
    ]

    const handleDropdownChange = (type: string, data: any) => {
        handleFilterOnChange({ type, data })
    }

    return (
        <>
            <ListHeader
                icon={
                    <SealogsCrewIcon
                        className="size-12"
                    />
                }
                title="All crew"
                actions={
                    <CrewFilterActions
                        onChange={(data: any) => {
                            handleDropdownChange('isArchived', data)
                        }}
                    />
                }
            />
            <div className="mt-16">
                <DataTable
                    isLoading={queryCrewMembersLoading}
                    columns={columns}
                    data={filteredCrewList}
                    pageSize={20}
                    onChange={handleFilterOnChange}
                />
            </div>
        </>
    )
}
// ---------------------------------------------------------------------------------------//
export const CrewTable = ({
    crewList,
    vessels,
    handleCrewDuty = false,
    showSurname,
}: any) => {
    const [isAdmin, setIsAdmin] = useState(false)
    const [departments, setDepartments] = useState([] as any)
    const [isLoading, setIsLoading] = useState(true)
    const renderDepartment = (
        departments: any[],
        parentID: number = 0,
        depth: number = 0,
    ): any[] => {
        return departments
            .filter((department: any) => +department.parentID === parentID)
            .flatMap((department: any) => {
                const children = renderDepartment(
                    departments,
                    +department.id,
                    depth + 1,
                )
                const item = {
                    ...department,
                    level: depth,
                }
                return [item, ...children]
            })
    }
    const [readDepartments, { loading: readDepartmentsLoading }] = useLazyQuery(
        ReadDepartments,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readDepartments.nodes
                if (data) {
                    const formattedData = renderDepartment(data)
                    setDepartments(formattedData)
                }
            },
            onError: (error: any) => {
                console.error('queryCrewMembers error', error)
            },
        },
    )
    const loadDepartments = async () => {
        await readDepartments()
    }
    useEffect(() => {
        if (isLoading) {
            loadDepartments()
            setIsLoading(false)
        }
    }, [isLoading])
    const crewListWithTrainingStatus = GetCrewListWithTrainingStatus(
        crewList,
        vessels,
    )
    const transformedCrewList = crewListWithTrainingStatus.map(
        (crewMember: any) => {
            const filteredDues = crewMember.trainingStatus.dues.filter(
                (due: any) => {
                    return crewMember.vehicles.nodes.some(
                        (node: any) => node.id === due.vesselID,
                    )
                },
            )
            const updatedTrainingStatus = {
                ...crewMember.trainingStatus,
                dues: filteredDues,
            }
            if (filteredDues.length === 0) {
                updatedTrainingStatus.label = 'Good'
            }
            return { ...crewMember, trainingStatus: updatedTrainingStatus }
        },
    )

    useEffect(() => {
        if (
            typeof window !== 'undefined' &&
            typeof window.localStorage !== 'undefined'
        ) {
            const result = localStorage.getItem('admin')
            const admin = result === 'true'
            setIsAdmin(admin)
        }
    }, [])

    const columns = createColumns([
        {
            accessorKey: 'title',
            header: '',
            cell: ({ row }: { row: any }) => {
                const crewMember: any = row.original
                return (
                    <div>
                        <div className="flex gap-2.5 items-center">
                            <Avatar
                                size="sm"
                                variant={
                                    crewMember.trainingStatus?.label !== 'Good'
                                        ? 'destructive'
                                        : 'success'
                                }>
                                <AvatarFallback>
                                    {getCrewInitials(
                                        crewMember.firstName,
                                        crewMember.surname,
                                    )}
                                </AvatarFallback>
                            </Avatar>
                            <Link
                                href={`/crew/info?id=${crewMember.id}`}
                                className="flex items-center pl-2 text-nowrap">
                                {crewMember.firstName || '--'}
                                {showSurname == true ? (
                                    <span>
                                        &nbsp;
                                        {crewMember.surname || '--'}
                                    </span>
                                ) : (
                                    <span className="hidden md:flex">
                                        &nbsp;
                                        {crewMember.surname || '--'}
                                    </span>
                                )}
                            </Link>
                        </div>

                        {handleCrewDuty && (
                            <div className="flex md:hidden flex-col">
                                {crewMember.vehicles.nodes &&
                                    crewMember.vehicles.nodes.map(
                                        (vessel: any, index: number) => {
                                            if (index < 2) {
                                                return (
                                                    <div
                                                        key={vessel.id}
                                                        className="bg-muted font-light rounded-lg p-2 border m-1 border-border text-nowrap">
                                                        <Link
                                                            className="max-w-32 overflow-hidden block"
                                                            href={`/vessel/info?id=${vessel.id}`}>
                                                            {vessel.title}
                                                        </Link>
                                                    </div>
                                                )
                                            }
                                            if (index === 2) {
                                                return (
                                                    <Popover key={vessel.id}>
                                                        <PopoverTrigger asChild>
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                className="text-orange-500">
                                                                +{' '}
                                                                {crewMember
                                                                    .vehicles
                                                                    .nodes
                                                                    .length -
                                                                    2}{' '}
                                                                more
                                                            </Button>
                                                        </PopoverTrigger>
                                                        <PopoverContent className="p-0 w-64">
                                                            <div className="max-h-full bg-background rounded">
                                                                {crewMember.vehicles.nodes
                                                                    .slice(2)
                                                                    .map(
                                                                        (
                                                                            v: any,
                                                                        ) => (
                                                                            <div
                                                                                key={
                                                                                    v.id
                                                                                }
                                                                                className="flex cursor-pointer hover:bg-muted items-center overflow-auto px-3 py-2">
                                                                                <div className="text-sm">
                                                                                    <Link
                                                                                        href={`/vessel/info?id=${v.id}`}>
                                                                                        {
                                                                                            v.title
                                                                                        }
                                                                                    </Link>
                                                                                </div>
                                                                            </div>
                                                                        ),
                                                                    )}
                                                            </div>
                                                        </PopoverContent>
                                                    </Popover>
                                                )
                                            }
                                            return null
                                        },
                                    )}
                            </div>
                        )}
                    </div>
                )
            },
        },
        {
            accessorKey: 'vessel',
            header: () => <>{handleCrewDuty && 'Vessel'}</>,
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const crew = row.original
                return (
                    <>
                        {handleCrewDuty && (
                            <div>
                                {crew.vehicles.nodes &&
                                    crew.vehicles.nodes.map(
                                        (vessel: any, index: number) => {
                                            if (index < 2) {
                                                return (
                                                    <div
                                                        key={vessel.id}
                                                        className="bg-muted inline-block font-light rounded-lg p-2 border border-border m-1">
                                                        <Link
                                                            href={`/vessel/info?id=${vessel.id}`}>
                                                            {vessel.title}
                                                        </Link>
                                                    </div>
                                                )
                                            }
                                            if (index === 2) {
                                                return (
                                                    <Popover key={vessel.id}>
                                                        <PopoverTrigger asChild>
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                className="text-orange-500">
                                                                +{' '}
                                                                {crew.vehicles
                                                                    .nodes
                                                                    .length -
                                                                    2}{' '}
                                                                more
                                                            </Button>
                                                        </PopoverTrigger>
                                                        <PopoverContent className="p-0 w-64">
                                                            <div className="max-h-full bg-background rounded">
                                                                {crew.vehicles.nodes
                                                                    .slice(2)
                                                                    .map(
                                                                        (
                                                                            v: any,
                                                                        ) => (
                                                                            <div
                                                                                key={
                                                                                    v.id
                                                                                }
                                                                                className="flex cursor-pointer hover:bg-muted items-center overflow-auto px-3 py-2">
                                                                                <div className="text-sm">
                                                                                    <Link
                                                                                        href={`/vessel/info?id=${v.id}`}>
                                                                                        {
                                                                                            v.title
                                                                                        }
                                                                                    </Link>
                                                                                </div>
                                                                            </div>
                                                                        ),
                                                                    )}
                                                            </div>
                                                        </PopoverContent>
                                                    </Popover>
                                                )
                                            }
                                            return null
                                        },
                                    )}
                            </div>
                        )}
                    </>
                )
            },
        },
        {
            accessorKey: 'primaryDuty',
            header: 'Primary Duty',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const crew = row.original
                return (
                    <div className="text-wrap text-right whitespace-normal">
                        {crew.primaryDuty.title}
                    </div>
                )
            },
        },
        {
            accessorKey: 'trainingStatus',
            header: 'Training status',
            cell: ({ row }: { row: any }) => {
                const crew = row.original

                return (
                    <div className="flex justify-center">
                        {crew.trainingStatus.label !== 'Good' ? (
                            <Popover triggerType="hover">
                                <PopoverTrigger asChild>
                                    <CircleAlert
                                        strokeWidth={1}
                                        className="h-9 w-9 text-destructive cursor-pointer"
                                    />
                                </PopoverTrigger>
                                <PopoverContent>
                                    <div className="bg-background rounded p-2">
                                        <div className="text-xs whitespace-nowrap font-medium focus:outline-none inline-block rounded">
                                            {crew.trainingStatus.dues.map(
                                                (
                                                    item: any,
                                                    dueIndex: number,
                                                ) => (
                                                    <div key={dueIndex}>
                                                        {`${item.trainingType.title} - ${item.status.label}`}
                                                    </div>
                                                ),
                                            )}
                                        </div>
                                    </div>
                                </PopoverContent>
                            </Popover>
                        ) : (
                            <Badge variant="success" type="circle">
                                <Check className="h-5 w-5" />
                            </Badge>
                        )}
                    </div>
                )
            },
        },
        {
            accessorKey: 'departments',
            header: () => (
                <>
                    {isAdmin &&
                        localStorage.getItem('useDepartment') === 'true' &&
                        'Departments'}
                </>
            ),
            cell: ({ row }: { row: any }) => {
                const crew = row.original
                return (
                    <div>
                        {isAdmin &&
                            localStorage.getItem('useDepartment') ===
                                'true' && (
                                <>
                                    {crew.departments &&
                                    crew.departments.nodes.length > 0 ? (
                                        crew.departments.nodes.map(
                                            (department: any) => (
                                                <Link
                                                    key={department.id}
                                                    href={`/department/info?id=${department.id}`}
                                                    className="flex flex-col text-nowrap">
                                                    {
                                                        departments.find(
                                                            (dept: any) =>
                                                                dept.id ===
                                                                department.id,
                                                        )?.title
                                                    }
                                                </Link>
                                            ),
                                        )
                                    ) : (
                                        <span>No departments found</span>
                                    )}
                                </>
                            )}
                    </div>
                )
            },
        },
    ])

    return (
        <DataTable
            columns={columns}
            showToolbar={false}
            data={transformedCrewList}
            pageSize={20}
        />
    )
}
