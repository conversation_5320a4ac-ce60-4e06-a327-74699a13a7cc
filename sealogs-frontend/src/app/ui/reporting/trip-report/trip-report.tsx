'use client'

import React, { useMemo, useState, useEffect } from 'react'
import { useLazyQuery } from '@apollo/client'
import { isEmpty } from 'lodash'
import { useRouter } from 'next/navigation'
import { ReadTripReport_LogBookEntrySections } from './query/readTripReport_LogBookEntrySections'
import dayjs from 'dayjs'
import { formatDate } from '@/app/helpers/dateHelper'
import { exportCsv } from '@/app/helpers/csvHelper'
import { createColumns, DataTable } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'
import { VesselLocationDisplay } from '@/components/ui/vessel-location-display'
import { VESSEL_BRIEF_LIST } from '@/app/lib/graphQL/query'
import { <PERSON><PERSON>, <PERSON>Header } from '@/components/ui'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { SealogsCogIcon } from '@/app/lib/icons'
import { useSidebar } from '@/components/ui/sidebar'

interface DateRange {
    startDate: Date | null
    endDate: Date | null
}

interface IDropdownItem {
    label: string
    value: string
}

type FilterType =
    | 'dateRange'
    | 'vessels'
    | 'fromLocation'
    | 'toLocation'
    | 'fromTime'
    | 'toTime'
    | 'noPax'
interface IFilter {
    type: FilterType
    data: any
}

interface ITripReportItem {
    id: string
    vesselName: string
    date: string
    departLocation: string
    totalPaxCarried: number
    totalVehicleCarried: number
    destination: string
    // Raw data for CSV export
    rawData: any
}

// Function to create columns (will be called inside component to access bp, vessel data, and vessels list)
const createTripReportColumns = (
    bp: any,
    getVesselWithIcon: any,
    vessels: any[] = [],
) =>
    createColumns<ITripReportItem>([
        {
            accessorKey: 'vesselName',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Vessel" />
            ),
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const item = row.original

                // Find the actual vessel by name from the vessels list
                const actualVessel = vessels.find(
                    (vessel: any) => vessel.title === item.vesselName,
                )

                if (actualVessel) {
                    // Use the actual vessel data with proper ID
                    const vesselWithIcon = getVesselWithIcon(
                        actualVessel.id,
                        actualVessel,
                    )
                    return (
                        <VesselLocationDisplay
                            vessel={vesselWithIcon}
                            vesselId={actualVessel.id}
                            displayText={item.vesselName}
                        />
                    )
                } else {
                    // Fallback for vessels not found in the list
                    const vesselForIcon = {
                        id: 0,
                        title: item.vesselName,
                    }
                    const vesselWithIcon = getVesselWithIcon(0, vesselForIcon)
                    return (
                        <VesselLocationDisplay
                            vessel={vesselWithIcon}
                            displayText={item.vesselName}
                        />
                    )
                }
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.vesselName || ''
                const valueB = rowB?.original?.vesselName || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'date',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Date" />
            ),
            cellAlignment: 'left' as const,
            breakpoint: 'tablet-sm',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return <>{item.date}</>
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.date || ''
                const valueB = rowB?.original?.date || ''
                return dayjs(valueA).unix() - dayjs(valueB).unix()
            },
        },
        {
            accessorKey: 'departLocation',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Depart Location" />
            ),
            cellAlignment: 'left' as const,
            breakpoint: 'laptop',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return <>{item.departLocation}</>
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.departLocation || ''
                const valueB = rowB?.original?.departLocation || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'totalPaxCarried',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader
                    column={column}
                    title="Total Pax Carried"
                />
            ),
            cellAlignment: 'right' as const,
            breakpoint: 'desktop',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return <>{item.totalPaxCarried || '-'}</>
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.totalPaxCarried || 0
                const valueB = rowB?.original?.totalPaxCarried || 0
                return valueA - valueB
            },
        },
        {
            accessorKey: 'totalVehicleCarried',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader
                    column={column}
                    title="Total Vehicle Carried"
                />
            ),
            cellAlignment: 'right' as const,
            breakpoint: 'desktop',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return (
                    <>
                        {item.totalVehicleCarried > 0
                            ? item.totalVehicleCarried
                            : '-'}
                    </>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.totalVehicleCarried || 0
                const valueB = rowB?.original?.totalVehicleCarried || 0
                return valueA - valueB
            },
        },
        {
            accessorKey: 'destination',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Destination" />
            ),
            cellAlignment: 'left' as const,
            breakpoint: 'laptop',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return <>{item.destination}</>
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.destination || ''
                const valueB = rowB?.original?.destination || ''
                return valueA.localeCompare(valueB)
            },
        },
    ])

// Trip Report Actions Component
const TripReportActions = ({
    onDownloadCsv,
    onBack,
    disabled = false,
}: {
    onDownloadCsv: () => void
    onBack: () => void
    disabled?: boolean
}) => {
    const { isMobile } = useSidebar()

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <SealogsCogIcon size={36} />
            </DropdownMenuTrigger>
            <DropdownMenuContent
                side={isMobile ? 'bottom' : 'right'}
                align={isMobile ? 'end' : 'start'}>
                <DropdownMenuItem variant="backButton" onClick={onBack}>
                    Back to Reporting
                </DropdownMenuItem>
                <DropdownMenuItem className='px-[26px]' onClick={onDownloadCsv} disabled={disabled}>
                    Download CSV
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}

function TripReport() {
    const router = useRouter()
    const bp = useBreakpoints()
    const { getVesselWithIcon } = useVesselIconData()
    const [filters, setFilters] = useState({} as any)
    const [tripReports, setTripReports] = useState([])
    const [vessels, setVessels] = useState<any[]>([])
    const [hasActiveFilters, setHasActiveFilters] = useState(false)

    // Load vessels for vessel lookup by name
    const [queryVessels] = useLazyQuery(VESSEL_BRIEF_LIST, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (queryVesselResponse: any) => {
            if (queryVesselResponse.readVessels.nodes) {
                const activeVessels =
                    queryVesselResponse.readVessels.nodes.filter(
                        (vessel: any) => !vessel.archived,
                    )
                setVessels(activeVessels)
            }
        },
        onError: (error: any) => {
            console.error('queryVessels error', error)
        },
    })

    // Load vessels on component mount
    useEffect(() => {
        queryVessels({
            variables: {
                limit: 200,
                offset: 0,
            },
        })
    }, [queryVessels])

    // Create columns with access to bp, vessel icon data, and vessels list
    const columns = createTripReportColumns(bp, getVesselWithIcon, vessels)

    const downloadCsv = () => {
        if (reportData.length === 0) {
            return
        }

        const csvEntries: string[][] = [
            [
                'Transit Trip ID',
                'Vessel',
                'Date',
                'Scheduled Depart Time',
                'Depart Time',
                'Depart Location',
                'Adult (boarded at origin)',
                'Passengers Onboard (at final destination)',
                'Total Pax Carried',
                'Trip Type',
                'Scheduled Arrive Time',
                'Arrival Time',
                'Destination',
                'Masters Remarks',
                'Stop',
                'Arrive Time',
                'Pax Off',
                'Pax On',
                'Depart Time',
            ],
        ]

        reportData.forEach((item: ITripReportItem) => {
            const trip = item.rawData
            const baseData = [
                trip.tripReportSchedule?.transitTripID ?? '', // Transit Trip ID
                trip.logBookEntry.vehicle.title, // Vessel
                formatDate(trip.logBookEntry.startDate, false), // Date
                trip.tripScheduleDepartTime ?? '', // Scheduled Depart Time
                trip.departTime ?? '', // Depart Time
                trip.fromLocation.title ?? '', // Depart Location
                trip.pob > 0 ? trip.pob.toString() : '', // Adult (boarded at origin)
            ]

            if (trip.tripReport_Stops?.nodes?.length > 0) {
                trip.tripReport_Stops.nodes.forEach((stop: any) => {
                    const rowData = [...baseData]
                    rowData.push(
                        trip.pob - stop.paxDeparted + stop.paxJoined > 0
                            ? (
                                  trip.pob -
                                  stop.paxDeparted +
                                  stop.paxJoined
                              ).toString()
                            : '', // Passengers On Board (at final destination)
                        trip.pob + stop.paxJoined > 0
                            ? (trip.pob + stop.paxJoined).toString()
                            : '', // Total Pax Carried
                        trip.tripReportScheduleID > 0
                            ? 'Scheduled'
                            : 'Unscheduled', // Trip Type
                        trip.tripScheduleArriveTime ?? '', // Scheduled Arrive Time
                        trip.arriveTime ?? '', // Arrival Time
                        trip.toLocation?.title ?? '', // Destination
                        trip.comment ?? '', // Masters Remarks
                        stop.stopLocation?.title ?? '', // Stop
                        stop.arriveTime ?? '', // Arrive Time
                        stop.paxDeparted?.toString() ?? '', // Pax Off
                        stop.paxJoined?.toString() ?? '', // Pax On
                        stop.departTime ?? '', // Depart Time
                    )
                    csvEntries.push(rowData)
                })
            } else {
                const rowData = [...baseData]
                rowData.push(
                    trip.pob > 0 ? trip.pob.toString() : '', // Passengers On Board (at final destination)
                    trip.pob > 0 ? trip.pob.toString() : '', // Total Pax Carried
                    trip.tripReportScheduleID > 0 ? 'Scheduled' : 'Unscheduled', // Trip Type
                    trip.tripScheduleArriveTime ?? '', // Scheduled Arrive Time
                    trip.arriveTime ?? '', // Arrival Time
                    trip.toLocation?.title ?? '', // Destination
                    trip.comment ?? '', // Masters Remarks
                    '',
                    '',
                    '',
                    '',
                    '', // Empty values for stop columns
                )
                csvEntries.push(rowData)
            }
        })

        const csvFilename = `trip-report-${dayjs().format('YYYY-MM-DD-HHmmss')}.csv`
        exportCsv(csvEntries, csvFilename)
    }

    const handleFilterOnChange = ({ type, data }: IFilter) => {
        const newFilters = { ...filters, [type]: data }
        setFilters(newFilters)

        // Check if user has actively selected specific filters
        const hasFilters = Object.keys(newFilters).some((key) => {
            const value = newFilters[key]
            if (key === 'dateRange') {
                return value?.startDate && value?.endDate
            }
            if (key === 'vessels') {
                return Array.isArray(value) ? value.length > 0 : !!value
            }
            return !!value
        })
        setHasActiveFilters(hasFilters)
    }

    const [
        readTripReport_LogBookEntrySections,
        { loading: readTripReport_LogBookEntrySectionsLoading },
    ] = useLazyQuery(ReadTripReport_LogBookEntrySections, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readTripReport_LogBookEntrySections.nodes
            setTripReports(data)
        },
        onError: (error: any) => {
            console.error('readTripReport_LogBookEntrySections error', error)
        },
    })

    const generateReport = () => {
        const filter: any = {
            archived: { eq: false },
        }
        // Process date range
        const fromDate = filters.dateRange?.startDate
            ? dayjs(filters.dateRange.startDate).format('YYYY-MM-DD')
            : null
        const toDate = filters.dateRange?.endDate
            ? dayjs(filters.dateRange.endDate).format('YYYY-MM-DD')
            : null

        if (fromDate && toDate) {
            filter.logBookEntry = {
                ...filter.logBookEntry,
                startDate: {
                    gte: fromDate,
                    lte: toDate,
                },
            }
        }
        // Process locations
        const fromLocationId = filters.fromLocation?.value
        const toLocationId = filters.toLocation?.value
        if (fromLocationId) {
            filter.fromLocationID = { eq: fromLocationId }
        }
        if (toLocationId) {
            filter.toLocationID = { eq: toLocationId }
        }
        // Process departure time
        const fromTime = filters.fromTime
        const toTime = filters.toTime
        if (fromTime && toTime) {
            filter.departTime = {
                gte: fromTime,
                lte: toTime,
            }
        } else if (fromTime) {
            filter.departTime = {
                gte: fromTime,
            }
        } else if (toTime) {
            filter.departTime = {
                lte: toTime,
            }
        }
        // Add numberPax filter if noPax is true
        if (filters.noPax) {
            filter.numberPax = { eq: 0 }
        }
        // Process vessels
        if (filters.vessels?.length > 0) {
            filter.logBookEntry = {
                ...filter.logBookEntry,
                vehicleID: {
                    in: filters.vessels.map((vessel: any) => vessel.value),
                },
            }
        }
        readTripReport_LogBookEntrySections({
            variables: {
                filter: filter,
                limit: 100,
                offset: 0,
            },
        })
    }

    // Transform raw trip data to report format
    const reportData = useMemo<ITripReportItem[]>(() => {
        if (tripReports.length === 0) {
            return []
        }

        return tripReports.map((trip: any) => {
            // Calculate total pax carried (same logic as original TableContent)
            const totalPaxCarried =
                trip.tripReport_Stops?.nodes?.length > 0
                    ? trip.pob +
                      trip.tripReport_Stops.nodes.reduce(
                          (total: number, stop: any) =>
                              total + (stop.paxJoined || 0),
                          0,
                      )
                    : trip.pob

            return {
                id: trip.id,
                vesselName: trip.logBookEntry.vehicle.title,
                date: formatDate(trip.logBookEntry.startDate),
                departLocation: trip.fromLocation.title,
                totalPaxCarried: totalPaxCarried,
                totalVehicleCarried: trip.vob || 0,
                destination: trip.toLocation.title,
                rawData: trip, // Keep raw data for CSV export
            }
        })
    }, [tripReports])

    // Load data automatically on mount, then only when user clicks generate report with active filters
    useEffect(() => {
        // Show results automatically on page load (as per memory for report pages)
        if (Object.keys(filters).length === 0) {
            generateReport()
        }
    }, [])

    // Only load when user has actively selected filters and they change
    useEffect(() => {
        if (hasActiveFilters && !isEmpty(filters)) {
            // Don't auto-load when user has active filters - require button click
        }
    }, [filters, hasActiveFilters])

    return (
        <>
            <ListHeader
                title="Trip Report"
                actions={
                    <TripReportActions
                        onDownloadCsv={downloadCsv}
                        onBack={() => router.push('/reporting')}
                        disabled={reportData.length === 0}
                    />
                }
            />
            <div className="mt-16">
                <DataTable
                    columns={columns}
                    data={reportData}
                    isLoading={readTripReport_LogBookEntrySectionsLoading}
                    onChange={handleFilterOnChange}
                    onFilterClick={generateReport}
                    noDataText="No trip data found, try clicking generate report to view results"
                    showToolbar={true}
                    filterProps={{
                        tripReportFilterData: filters,
                    }}
                />
            </div>
        </>
    )
}

export default TripReport
