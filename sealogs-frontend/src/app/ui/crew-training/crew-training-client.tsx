'use client'

import React, { useRef, useState, useEffect, useMemo, useCallback } from 'react'
import CrewTrainingList from './list'

import { CrewTrainingFilterActions } from '@/components/filter/components/training-actions'
import { SealogsTrainingIcon } from '@/app/lib/icons/SealogsTrainingIcon'
import { ListHeader } from '@/components/ui/list-header'
import { useQueryState, parseAsBoolean } from 'nuqs'
import { UnifiedTrainingTable } from './unified-training-table'
import { useLazyQuery } from '@apollo/client'
import {
    TRAINING_SESSIONS,
    READ_TRAINING_SESSION_DUES,
} from '@/app/lib/graphQL/query'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'
import { mergeAndSortCrewTrainingData } from '@/app/ui/crew-training/utils/crew-training-utils'
import { useUnifiedTrainingFilters } from './hooks/useUnifiedTrainingFilters'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import Loading from '@/app/loading'

type FilterHandle = {
    apply: (p: { type: string; data: any }) => void
    overdue: boolean // read-only snapshot
    setOverdue: (v: boolean) => void
}

const CrewTrainingClient = () => {
    const applyFilterRef = useRef<FilterHandle>(null)

    /** ⬅️ 1) reactive state that drives the heading */
    const [isOverdueEnabled, setIsOverdueEnabled] = useQueryState(
        'overdue',
        parseAsBoolean.withDefault(false),
    )

    // Training data state
    const [isLoading, setIsLoading] = useState(false)
    const [hasError, setHasError] = useState(false)
    const [trainingSessionDues, setTrainingSessionDues] = useState<any[]>([])
    const [completedTrainingList, setCompletedTrainingList] = useState<any[]>(
        [],
    )
    const [includeCompleted, setIncludeCompleted] = useState(true)
    const [permissions, setPermissions] = useState<any>(false)

    // Filter options extracted from data (like reference implementation)
    const [vesselIdOptions, setVesselIdOptions] = useState<any[]>([])
    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = useState<any[]>(
        [],
    )
    const [trainerIdOptions, setTrainerIdOptions] = useState<any[]>([])
    const [memberIdOptions, setMemberIdOptions] = useState<any[]>([])

    // Initialize permissions
    useEffect(() => {
        setPermissions(getPermissions)
    }, [])

    // GraphQL queries
    const [loadTrainingSessionDues] = useLazyQuery(READ_TRAINING_SESSION_DUES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (data) => {
            setTrainingSessionDues(data.readTrainingSessionDues?.nodes || [])
        },
        onError: (error) => {
            console.error('Error loading training session dues:', error)
            setHasError(true)
        },
    })

    const [loadTrainingList] = useLazyQuery(TRAINING_SESSIONS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readTrainingSessions?.nodes || []

            // Transform vessel data to include complete vessel information with position (like reference)
            const transformedData = data.map((item: any) => {
                const completeVesselData = getVesselWithIcon(
                    item.vessel?.id,
                    item.vessel,
                )
                return {
                    ...item,
                    vessel: completeVesselData,
                }
            })

            // Extract filter options from data (like reference implementation)
            const vesselIDs = Array.from(
                new Set(
                    data
                        .map((item: any) => item.vessel?.id)
                        .filter((id: any) => +id !== 0),
                ),
            )
            const trainingTypeIDs = Array.from(
                new Set(
                    data.flatMap(
                        (item: any) =>
                            item.trainingTypes?.nodes?.map((t: any) => t.id) ||
                            [],
                    ),
                ),
            )
            const trainerIDs = Array.from(
                new Set(
                    data
                        .map((item: any) => item.trainerID)
                        .filter((id: any) => +id !== 0),
                ),
            )
            const memberIDs = Array.from(
                new Set(
                    data.flatMap(
                        (item: any) =>
                            item.members?.nodes?.map((t: any) => t.id) || [],
                    ),
                ),
            )

            setCompletedTrainingList(transformedData)
            setVesselIdOptions(vesselIDs)
            setTrainingTypeIdOptions(trainingTypeIDs)
            setTrainerIdOptions(trainerIDs)
            setMemberIdOptions(memberIDs)
        },
        onError: (error) => {
            console.error('Error loading training sessions:', error)
            setHasError(true)
        },
    })

    // Vessel icon helper
    const { getVesselWithIcon } = useVesselIconData()

    // Memoize the unified data calculation
    const unifiedData = useMemo(() => {
        return mergeAndSortCrewTrainingData({
            trainingSessionDues,
            completedTrainingList,
            getVesselWithIcon,
            includeCompleted,
        })
    }, [
        trainingSessionDues,
        completedTrainingList,
        getVesselWithIcon,
        includeCompleted,
    ])

    // Use unified training filters hook with client-side filtering
    const { handleFilterChange, filteredData } = useUnifiedTrainingFilters({
        initialFilter: {},
        unifiedData,
    })

    // Load data on component mount
    useEffect(() => {
        const loadData = async () => {
            setIsLoading(true)
            setHasError(false)
            try {
                await Promise.all([
                    loadTrainingSessionDues({
                        variables: {
                            filter: {},
                        },
                    }),
                    loadTrainingList({
                        variables: {
                            filter: {},
                            offset: 0,
                            limit: 100,
                        },
                    }),
                ])
            } catch (error) {
                console.error('Error loading training data:', error)
                setHasError(true)
            } finally {
                setIsLoading(false)
            }
        }

        loadData()
    }, [loadTrainingSessionDues, loadTrainingList])

    /** ⬅️ 2) keep both the list and the heading in sync */
    const handleDropdownChange = (type: string, data: any) => {
        if (type === 'overdue') setIsOverdueEnabled(!!data)
        applyFilterRef.current?.apply({ type, data })
    }

    // Handle filter changes from the FilterTable toolbar
    const handleFilterTableChange = useCallback(
        (filterData: { type: string; data: any }) => {
            handleFilterChange(filterData)
        },
        [handleFilterChange],
    )

    // Check permissions - CRITICAL SECURITY GUARD
    if (
        !permissions ||
        (!hasPermission('EDIT_TRAINING', permissions) &&
            !hasPermission('VIEW_TRAINING', permissions) &&
            !hasPermission('RECORD_TRAINING', permissions) &&
            !hasPermission('VIEW_MEMBER_TRAINING', permissions))
    ) {
        return !permissions ? (
            <Loading />
        ) : (
            <Loading errorMessage="Oops! You do not have the permission to view this section." />
        )
    }

    // Handle error state
    if (hasError) {
        return (
            <Loading errorMessage="Failed to load training data. Please try refreshing the page." />
        )
    }

    return (
        <>
            <ListHeader
                icon={<SealogsTrainingIcon className="size-12" />}
                title={'Crew Trainings'}
                actions={
                    <CrewTrainingFilterActions
                        onChange={(data: any) => {
                            handleDropdownChange('overdue', data)
                        }}
                        overdueList={isOverdueEnabled}
                    />
                }
            />
            <div className="mt-16">
                <UnifiedTrainingTable
                    unifiedData={filteredData}
                    getVesselWithIcon={getVesselWithIcon}
                    includeCompleted={includeCompleted}
                    isVesselView={false}
                    showToolbar={true}
                    isLoading={isLoading}
                    pageSize={20}
                    onChange={handleFilterTableChange}
                    filterProps={{
                        vesselIdOptions,
                        trainingTypeIdOptions,
                        trainerIdOptions,
                        memberIdOptions,
                    }}
                />
            </div>
        </>
    )
}

export default CrewTrainingClient
