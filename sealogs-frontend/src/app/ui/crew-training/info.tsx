'use client'

import { redirect, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import Link from 'next/link'
import { TrainingSessionInfoSkeleton } from '@/components/skeletons'
import Image from 'next/image'
import { getTrainingSessionByID } from '@/app/lib/actions'
import { formatDate } from '@/app/helpers/dateHelper'
import { stripHtmlTags } from '@/app/helpers/stringHelper'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { H4, P } from '@/components/ui/typography'
import { GraduationCap } from 'lucide-react'
import {
    CheckField,
    CheckFieldContent,
    CheckFieldTopContent,
    DailyCheckField,
} from '@/components/daily-check-field'
import {
    Card,
    ListHeader,
    Tooltip,
    TooltipContent,
    TooltipTrigger,
} from '@/components/ui'
import { Avatar, AvatarFallback, getCrewInitials } from '@/components/ui/avatar'
import { GET_SECTION_MEMBER_IMAGES } from '@/app/lib/graphQL/query'
import { useLazyQuery } from '@apollo/client'
import { SealogsTrainingIcon } from '@/app/lib/icons'
import { FooterWrapper } from '@/components/footer-wrapper'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'
import { getResponsiveLabel } from '../../../../utils/responsiveLabel'

const CrewTrainingInfo = ({ trainingID }: { trainingID: number }) => {
    if (trainingID <= 0) {
        redirect('/crew-training')
    }

    const [training, setTraining] = useState<any>()
    const [descriptionPanelContent, setDescriptionPanelContent] = useState('')
    const [openCommentAlert, setOpenCommentAlert] = useState(false)
    const [currentComment, setCurrentComment] = useState<any>('')
    const [fieldImages, setFieldImages] = useState<any>(false)
    const router = useRouter()
    const bp = useBreakpoints()

    getTrainingSessionByID(trainingID, setTraining)

    const [getFieldImages] = useLazyQuery(GET_SECTION_MEMBER_IMAGES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readCaptureImages.nodes
            if (data) {
                setFieldImages(data)
            }
        },
        onError: (error: any) => {
            console.error('getFieldImages error', error)
        },
    })

    useEffect(() => {
        getFieldImages({
            variables: {
                filter: {
                    trainingSessionID: { eq: trainingID },
                },
            },
        })
    }, [])

    const refreshImages = async () => {
        await getFieldImages({
            variables: {
                filter: {
                    trainingSessionID: { eq: trainingID },
                },
            },
        })
    }

    if (!training) {
        return <TrainingSessionInfoSkeleton />
    }

    const getProcedures = () => {
        return training?.trainingTypes?.nodes
            ?.map((type: any) => {
                return type.customisedComponentField.nodes.length > 0
                    ? {
                          id: type.id,
                          title: type.title,
                          fields: [
                              ...type.customisedComponentField.nodes,
                          ]?.sort(
                              (a: any, b: any) => a.sortOrder - b.sortOrder,
                          ),
                      }
                    : null
            })
            .filter((type: any) => type !== null)
    }

    const getFieldStatus = (field: any) => {
        const fieldStatus = training?.procedureFields?.nodes?.find(
            (procedureField: any) =>
                procedureField.customisedComponentFieldID == field.id,
        )
        return fieldStatus?.status || ''
    }
    const getComment = (field: any) => {
        const fieldComment = training?.procedureFields?.nodes?.find(
            (procedureField: any) =>
                procedureField.customisedComponentFieldID == field.id,
        )
        return fieldComment?.comment || field.comment
    }

    const showCommentPopup = (field: any) => {
        const fieldComment = training?.procedureFields?.nodes?.find(
            (procedureField: any) =>
                procedureField.customisedComponentFieldID == field.id,
        )
        setCurrentComment(fieldComment?.comment || '')
        setOpenCommentAlert(true)
    }

    return (
        <div className="space-y-6">
            {/* Header Section */}
            <ListHeader
                icon={<SealogsTrainingIcon className="size-12" />}
                title={`Training session: ${training?.vessel.title} - ${training?.date ? formatDate(training.date, false) : 'No date set'}`}
            />
            {/* Training Details Section */}
            <div className="mt-16 space-y-6 mx-2">
                <Card>
                    <div>
                        <H4>Training details</H4>
                        <P>
                            Information about the trainer, type of training
                            conducted, and participating crew members.
                        </P>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                            <h5 className="font-medium text-foreground">
                                Trainer
                            </h5>
                            <p className="text-base">{`${training?.trainer?.firstName || ''} ${training?.trainer?.surname || ''}`}</p>
                        </div>

                        <div className="space-y-2">
                            <h5 className="font-medium text-foreground">
                                Nature of training
                            </h5>
                            <p className="text-base">
                                {training?.trainingTypes?.nodes
                                    .map((t: any) => t.title)
                                    .join(', ')}
                            </p>
                        </div>
                    </div>

                    <div className="space-y-4 mt-6">
                        <div>
                            <h5 className="font-medium text-foreground mb-3">
                                Participating crew members
                            </h5>
                            <div className="flex flex-wrap gap-3">
                                {training?.members?.nodes.map(
                                    (m: any, index: number) => (
                                        <Tooltip key={index}>
                                            <TooltipTrigger>
                                                <Avatar
                                                    size="md"
                                                    variant="secondary">
                                                    <AvatarFallback className="text-sm">
                                                        {getCrewInitials(
                                                            m.firstName,
                                                            m.surname,
                                                        )}
                                                    </AvatarFallback>
                                                </Avatar>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>{`${m.firstName || ''} ${m.surname || ''}`}</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    ),
                                )}
                            </div>
                        </div>
                    </div>
                </Card>

                {/* Training Summary Section */}
                <Card className="space-y-4">
                    <div>
                        <H4>Training summary</H4>
                        <P>
                            Training procedures completed and overall session
                            summary.
                        </P>
                    </div>
                    <div className="bg-muted p-4 rounded-md">
                        {getProcedures().length > 0 && (
                            <div className="space-y-4 mb-4">
                                {getProcedures().map((type: any) => (
                                    <div
                                        key={type.id}
                                        className="bg-sllightblue-100 border border-sllightblue-200 rounded-md p-4">
                                        <h3 className="text-lg font-medium leading-6 text-gray-9000 mb-4">
                                            {type.title}
                                        </h3>
                                        <CheckField>
                                            {type.fields.filter(
                                                (field: any) =>
                                                    field.status === 'Required',
                                            ).length > 0 && (
                                                <CheckFieldTopContent />
                                            )}
                                            <CheckFieldContent>
                                                {type.fields.map(
                                                    (field: any) => (
                                                        <DailyCheckField
                                                            locked={true}
                                                            key={field.id}
                                                            displayField={
                                                                field.status ===
                                                                'Required'
                                                            }
                                                            displayDescription={
                                                                field.description
                                                            }
                                                            displayLabel={
                                                                field.fieldName
                                                            }
                                                            inputId={field.id}
                                                            defaultNoChecked={
                                                                getFieldStatus(
                                                                    field,
                                                                ) === 'Not_Ok'
                                                            }
                                                            defaultYesChecked={
                                                                getFieldStatus(
                                                                    field,
                                                                ) === 'Ok'
                                                            }
                                                            commentAction={() =>
                                                                showCommentPopup(
                                                                    field,
                                                                )
                                                            }
                                                            comment={getComment(
                                                                field,
                                                            )}
                                                            handleNoChange={() => {}}
                                                            handleYesChange={() => {}}
                                                            displayImage={true}
                                                            fieldImages={
                                                                fieldImages
                                                            }
                                                            onImageUpload={
                                                                refreshImages
                                                            }
                                                            sectionData={{
                                                                id: trainingID,
                                                                sectionName:
                                                                    'trainingSessionID',
                                                            }}
                                                        />
                                                    ),
                                                )}
                                            </CheckFieldContent>
                                        </CheckField>
                                    </div>
                                ))}
                            </div>
                        )}
                        <div className="whitespace-pre-line">
                            {training.trainingSummary
                                ? stripHtmlTags(training.trainingSummary)
                                : 'No summary provided.'}
                        </div>
                    </div>
                </Card>

                {/* Signatures Section */}
                <Card className="space-y-6">
                    <div>
                        <H4>Signatures</H4>
                        <P>
                            Digital signatures from training participants
                            confirming completion.
                        </P>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                        {training.signatures?.nodes.map((s: any) => (
                            <div
                                key={s.memberID}
                                className="border border-border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                                <div className="p-4 bg-accent">
                                    <h5 className="text-base font-medium">
                                        {s.member.firstName} {s.member.surname}
                                    </h5>
                                </div>
                                <div className="bg-background border-t border-border p-4 h-[120px] flex items-center justify-center">
                                    {s.signatureData ? (
                                        <Image
                                            src={
                                                s.signatureData ||
                                                '/placeholder.svg'
                                            }
                                            alt={`Signature of ${s.member.firstName} ${s.member.surname}`}
                                            width={220}
                                            height={80}
                                            className="object-contain"
                                        />
                                    ) : (
                                        <span className="text-muted-foreground text-sm italic">
                                            No signature provided
                                        </span>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </Card>
            </div>
            {/* Footer Information */}
            <FooterWrapper className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                    <span>
                        {getResponsiveLabel(
                            bp.phablet,
                            `ID: ${training.id}`,
                            `Training ID: ${training.id}`,
                        )}
                    </span>
                    <Separator
                        orientation="vertical"
                        className="h-4 hidden small:block"
                    />
                    <span className="hidden small:block">
                        {getResponsiveLabel(
                            bp.phablet,
                            `Updated: ${formatDate(training.updatedAt || training.date)}`,
                            `Last updated: ${formatDate(training.updatedAt || training.date)}`,
                        )}
                    </span>
                </div>
                <div className="flex gap-2.5 justify-end">
                    <Button
                        size="sm"
                        variant="back"
                        onClick={() => router.back()}>
                        Back
                    </Button>
                    <Button size="sm" asChild variant="outline">
                        <Link href={`/crew-training/edit?id=${training.id}`}>
                            {getResponsiveLabel(
                                bp.phablet,
                                `Edit`,
                                `Edit Session`,
                            )}
                        </Link>
                    </Button>
                </div>
            </FooterWrapper>
        </div>
    )
}

export default CrewTrainingInfo
