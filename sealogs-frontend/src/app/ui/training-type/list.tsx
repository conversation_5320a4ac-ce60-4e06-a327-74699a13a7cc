'use client'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { TrainingTypeType } from '../../../../types/training-type'
import { useLazyQuery } from '@apollo/client'
import { CREW_TRAINING_TYPES } from '@/app/lib/graphQL/query'
import { isEmpty, trim } from 'lodash'
import {
    createColumns,
    DataTable,
    ExtendedColumnDef,
} from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { ListHeader } from '@/components/ui/list-header'
import { SealogsTrainingIcon } from '@/app/lib/icons/SealogsTrainingIcon'
import { TrainingTypeFilterActions } from '@/components/filter/components/training-types-actions'
import Loading from '@/app/loading'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'
import VesselIcon from '@/app/ui/vessels/vesel-icon'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'

const TrainingScheduleList = () => {
    const [trainingTypes, setTrainingTypes] = useState([] as TrainingTypeType[])
    const [filter, setFilter] = useState({} as SearchFilter)
    const [isLoading, setIsLoading] = useState(true)
    const [keywordFilter, setKeywordFilter] = useState([] as any)
    const limit = 100
    const [page, setPage] = useState(0)
    const { getVesselWithIcon } = useVesselIconData()
    const bp = useBreakpoints()
    const [queryTrainingTypes, { loading: trainingTypesLoading }] =
        useLazyQuery(CREW_TRAINING_TYPES, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTrainingTypes.nodes
                const hasNextPage =
                    response.readTrainingTypes.pageInfo.hasNextPage
                const totalCount =
                    response.readTrainingTypes.pageInfo.totalCount
                if (data.length > 0) {
                    setTrainingTypes([...trainingTypes, ...data])
                    if (hasNextPage) {
                        const nextPage = page + 1
                        setPage(nextPage)
                        queryTrainingTypes({
                            variables: {
                                limit: limit,
                                offset: nextPage * limit,
                                filter: { ...filter, ...keywordFilter },
                            },
                        })
                    }
                }
            },
            onError: (error: any) => {
                console.error('queryTrainingTypes error', error)
            },
        })
    useEffect(() => {
        if (isLoading) {
            setTrainingTypes([])
            setPage(0)
            loadTrainingTypes()
            setIsLoading(false)
        }
    }, [isLoading])
    const loadTrainingTypes = async (
        searchFilter: SearchFilter = { ...filter },
        searchkeywordFilter: any = keywordFilter,
    ) => {
        if (searchkeywordFilter.length > 0) {
            const promises = searchkeywordFilter.map(
                async (keywordFilter: any) => {
                    return await queryTrainingTypes({
                        variables: {
                            filter: { ...searchFilter, ...keywordFilter },
                        },
                    })
                },
            )
            let responses = await Promise.all(promises)
            // filter out empty results
            responses = responses.filter(
                (r: any) => r.data.readTrainingTypes.nodes.length > 0,
            )
            // flatten results
            responses = responses.flatMap(
                (r: any) => r.data.readTrainingTypes.nodes,
            )
            // filter out duplicates
            responses = responses.filter(
                (value: any, index: any, self: any) =>
                    self.findIndex((v: any) => v.id === value.id) === index,
            )
            setTrainingTypes(responses)
        } else {
            await queryTrainingTypes({
                variables: {
                    filter: searchFilter,
                },
            })
        }
    }

    const handleFilterOnChange = ({ type, data }: any) => {
        const searchFilter: SearchFilter = { ...filter }
        if (type === 'vessel') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.vessels = {
                    id: { in: data.map((item) => +item.value) },
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.vessels = { id: { eq: +data.value } }
            } else {
                delete searchFilter.vessels
            }
        }
        let keyFilter = keywordFilter
        if (type === 'keyword') {
            if (!isEmpty(trim(data.value))) {
                keyFilter = [
                    { title: { contains: data.value } },
                    { procedure: { contains: data.value } },
                ]
            } else {
                keyFilter = []
            }
        }
        setFilter(searchFilter)
        setKeywordFilter(keyFilter)
        setTrainingTypes([])
        setPage(0)
        loadTrainingTypes(searchFilter, keyFilter)
    }
    const columns = createColumns([
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader
                    column={column}
                    title="Nature of Training"
                />
            ),
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const trainingType: any = row.original
                return (
                    <Link
                        href={`/training-type/info?id=${trainingType.id}`}
                        className="hover:underline">
                        {trainingType.title}
                    </Link>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.title || ''
                const valueB = rowB?.original?.title || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'vessels',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Vessels" />
            ),
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const trainingType = row.original

                const number = bp.small ? 2 : 1
                return (
                    <div className="flex sm:flex-wrap gap-2">
                        {trainingType?.vessels?.nodes.map(
                            (vessel: any, index: number) => {
                                if (index < number) {
                                    const vesselWithIcon = getVesselWithIcon(
                                        vessel.id,
                                        vessel,
                                    )
                                    return (
                                        <div key={vessel.id}>
                                            {!bp['tablet-md'] ? (
                                                <div className="size-11 flex items-center justify-center flex-shrink-0 [&_img]:!size-11 [&_svg]:!size-11">
                                                    <VesselIcon
                                                        vessel={vesselWithIcon}
                                                    />
                                                </div>
                                            ) : (
                                                <Badge
                                                    key={vessel.id}
                                                    variant="outline"
                                                    type="normal"
                                                    className="font-normal flex items-center gap-2">
                                                    <div className="size-6 flex items-center justify-center flex-shrink-0 [&_img]:!size-6 [&_svg]:!size-6">
                                                        <VesselIcon
                                                            vessel={
                                                                vesselWithIcon
                                                            }
                                                        />
                                                    </div>
                                                    {vessel.title}
                                                </Badge>
                                            )}
                                        </div>
                                    )
                                }
                                if (index === number) {
                                    return (
                                        <Popover key={vessel.id}>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className="!p-2 bg-transparent flex-none">
                                                    +
                                                    {trainingType.vessels.nodes
                                                        .length - number}{' '}
                                                    more
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-64 p-2">
                                                <div className="space-y-1">
                                                    {trainingType.vessels.nodes
                                                        .slice(number)
                                                        .map((vessel: any) => {
                                                            const vesselWithIcon =
                                                                getVesselWithIcon(
                                                                    vessel.id,
                                                                    vessel,
                                                                )
                                                            return (
                                                                <div
                                                                    key={
                                                                        vessel.id
                                                                    }
                                                                    className="px-2 py-1 text-sm hover:bg-muted rounded flex items-center gap-2">
                                                                    <div className="size-6 flex items-center justify-center flex-shrink-0 [&_img]:!size-6 [&_svg]:!size-6">
                                                                        <VesselIcon
                                                                            vessel={
                                                                                vesselWithIcon
                                                                            }
                                                                        />
                                                                    </div>
                                                                    {
                                                                        vessel.title
                                                                    }
                                                                </div>
                                                            )
                                                        })}
                                                </div>
                                            </PopoverContent>
                                        </Popover>
                                    )
                                }
                                return null
                            },
                        )}
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.vessels?.nodes?.[0]?.title || ''
                const valueB = rowB?.original?.vessels?.nodes?.[0]?.title || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'occursEvery',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader
                    column={column}
                    title="Occurs Every (days)"
                />
            ),
            breakpoint: 'desktop',
            cell: ({ row }: { row: any }) => {
                const trainingType = row.original
                return (
                    <span>
                        {trainingType.occursEvery
                            ? trainingType.occursEvery
                            : '-'}
                    </span>
                )
            },
        },
        {
            accessorKey: 'details',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Other details" />
            ),
            breakpoint: 'laptop',
            cellAlignment: 'right' as const,
            cell: ({ row }: { row: any }) => {
                const trainingType = row.original
                return (
                    <div className="space-y-2 flex flex-col items-end">
                        {trainingType.occursEvery ? (
                            <div className="w-fit desktop:hidden flex items-center gap-2">
                                <span className="text-sm font-medium">
                                    Occurs Every:
                                </span>
                                <Badge
                                    variant="secondary"
                                    type="normal"
                                    className="size-fit py-1 px-2">
                                    {trainingType.occursEvery} days
                                </Badge>
                            </div>
                        ) : null}

                        {trainingType.mediumWarnWithin != 0 ? (
                            <div className="w-fit flex items-center gap-2">
                                <span className="text-sm">
                                    Medium Warning Within:
                                </span>
                                <Badge
                                    variant="warning"
                                    type="normal"
                                    className="size-fit py-1 px-2">
                                    {trainingType.mediumWarnWithin} days
                                </Badge>
                            </div>
                        ) : null}

                        {trainingType.highWarnWithin ? (
                            <div className="w-fit flex items-center gap-2">
                                <span className="text-sm">
                                    High Warning Within:
                                </span>
                                <Badge
                                    variant="destructive"
                                    type="normal"
                                    className="size-fit py-1 px-2">
                                    {trainingType.highWarnWithin} days
                                </Badge>
                            </div>
                        ) : null}
                    </div>
                )
            },
        },
    ])
    return (
        <>
            <ListHeader
                icon={
                    <SealogsTrainingIcon className="size-12" />
                }
                title="Training Types"
                actions={<TrainingTypeFilterActions />}
            />
            <div className="mt-16">
                {isLoading || trainingTypesLoading ? (
                    <Loading />
                ) : (
                    <DataTable
                        columns={columns as ExtendedColumnDef<any, unknown>[]}
                        data={trainingTypes}
                        pageSize={10}
                        onChange={handleFilterOnChange}
                        showToolbar={true}
                    />
                )}
            </div>
        </>
    )
}

export default TrainingScheduleList
