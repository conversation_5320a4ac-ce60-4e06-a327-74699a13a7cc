'use client'
import React, { useEffect, useState } from 'react'

import Loading from '@/app/loading'
import { useLazyQuery } from '@apollo/client'

import {
    VESSEL_LIST_WITH_DOCUMENTS,
    GET_INVENTORIES_WITH_DOCUMENTS,
    MAINTENANCE_LIST_WITH_DOCUMENT,
    READ_ONE_CLIENT,
} from '@/app/lib/graphQL/query'
import { isEmpty, trim } from 'lodash'
import { formatDate } from '@/app/helpers/dateHelper'
import { DataTable, createColumns } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { ListHeader } from '@/components/ui/list-header'
import { SealogsDocumentLockerIcon } from '@/app/lib/icons/SealogsDocumentLockerIcon'
import { Badge, P } from '@/components/ui'
import { ExternalLink } from 'lucide-react'

export default function DocumentList() {
    const [vesselListWithDocuments, setVesselListWithDocuments] = useState(
        [] as any,
    )
    const [vesselListWithDocumentsCopy, setVesselListWithDocumentsCopy] =
        useState([] as any)

    const [filter, setFilter] = useState({} as SearchFilter)
    const [isLoading, setIsLoading] = useState(true)

    const [queryVesselListDocument] = useLazyQuery(VESSEL_LIST_WITH_DOCUMENTS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: async (response: any) => {
            let filterData = []
            const data = response.readVessels.nodes
            if (data && data.length) {
                for (const element of data) {
                    const documents = element.documents.nodes
                    for (const doc of documents) {
                        const modifiedDoc = {
                            ...doc,
                            type: element.__typename,
                            type_title: element.title,
                            type_id: element.id, // Assuming `id` is the unique identifier for the vessel
                        }
                        filterData.push(modifiedDoc)
                    }
                }
                setVesselListWithDocuments([
                    ...vesselListWithDocuments,
                    ...filterData,
                ])
                setVesselListWithDocumentsCopy([
                    ...vesselListWithDocuments,
                    ...filterData,
                ])
            }
        },
        onError: (error: any) => {
            console.error('queryInventoriesEntry error', error)
        },
    })

    const [queryInventoriesListDocument] = useLazyQuery(
        GET_INVENTORIES_WITH_DOCUMENTS,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: async (response: any) => {
                let filterData = []
                const data = response.readInventories.nodes
                if (data && data.length) {
                    for (const element of data) {
                        const documents = element.documents.nodes
                        for (const doc of documents) {
                            const modifiedDoc = {
                                ...doc,
                                type: element.__typename,
                                type_title: element.title,
                                type_id: element.id, // Assuming `id` is the unique identifier for the vessel
                            }
                            filterData.push(modifiedDoc)
                        }
                    }
                    setVesselListWithDocuments([
                        ...vesselListWithDocuments,
                        ...filterData,
                    ])
                    setVesselListWithDocumentsCopy([
                        ...vesselListWithDocuments,
                        ...filterData,
                    ])
                }
            },
            onError: (error: any) => {
                console.error('queryInventoriesEntry error', error)
            },
        },
    )

    const [queryMaintenanceListDocument] = useLazyQuery(
        MAINTENANCE_LIST_WITH_DOCUMENT,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: async (response: any) => {
                let filterData = []
                const data = response.readComponentMaintenanceChecks.nodes
                if (data && data.length) {
                    for (const element of data) {
                        const documents = element.documents.nodes
                        for (const doc of documents) {
                            const modifiedDoc = {
                                ...doc,
                                type: 'Maintenance',
                                type_title: element.name,
                                type_id: element.id, // Assuming `id` is the unique identifier for the vessel
                            }
                            filterData.push(modifiedDoc)
                        }
                    }
                    setVesselListWithDocuments([
                        ...vesselListWithDocuments,
                        ...filterData,
                    ])
                    setVesselListWithDocumentsCopy([
                        ...vesselListWithDocuments,
                        ...filterData,
                    ])
                }
            },
            onError: (error: any) => {
                console.error('queryInventoriesEntry error', error)
            },
        },
    )

    const handleFilterOnChange = ({ type, data }: any) => {
        let searchFilter = { ...filter }
        let updatedVesselList = vesselListWithDocumentsCopy

        if (type === 'vessel') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.vesselID = { in: data.map((item) => +item.value) }
                updatedVesselList = updatedVesselList.filter((item: any) =>
                    searchFilter.vesselID.in.includes(+item.type_id),
                )
            } else if (data && !Array.isArray(data)) {
                searchFilter.vesselID = { eq: +data.value }
                updatedVesselList = updatedVesselList.filter(
                    (item: any) => item.type_id === data.value.toString(),
                )
            } else {
                delete searchFilter.vesselID
            }
        }

        if (type === 'Module') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.moduleName = {
                    in: data.map((item: any) => item.value),
                }
                updatedVesselList = updatedVesselList.filter((item: any) =>
                    searchFilter.moduleName.in.includes(item.type),
                )
            } else if (data && data.value && !Array.isArray(data)) {
                searchFilter.moduleName = data.value
                updatedVesselList = updatedVesselList.filter(
                    (item: any) => item.type === data.value,
                )
            } else {
                delete searchFilter.moduleName
            }
        }

        if (type === 'keyword') {
            if (data?.value?.trim()) {
                const lowerCaseContains = data.value.trim().toLowerCase()
                searchFilter.item = { contains: lowerCaseContains }

                updatedVesselList = updatedVesselList.filter((item: any) => {
                    const lowerCaseTitle = item.title.toLowerCase()
                    const lowerCaseName = item.name.toLowerCase()
                    return (
                        lowerCaseTitle.includes(lowerCaseContains) ||
                        lowerCaseName.includes(lowerCaseContains)
                    )
                })
            } else {
                delete searchFilter.item
            }
        }

        setFilter(searchFilter)
        setVesselListWithDocuments(updatedVesselList)
    }

    const [readOneClient] = useLazyQuery(READ_ONE_CLIENT, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneClient
            if (data) {
                const docs = data.documents.nodes.map((doc: any) => {
                    return {
                        ...doc,
                        type: 'Company',
                        type_title: '',
                        type_id: 0,
                    }
                })
                if (docs.length > 0) {
                    setVesselListWithDocuments([
                        ...vesselListWithDocuments,
                        ...docs,
                    ])
                    setVesselListWithDocumentsCopy([
                        ...vesselListWithDocuments,
                        ...docs,
                    ])
                }
            }
        },
        onError: (error: any) => {
            console.error('readOneClient error', error)
        },
    })
    const loadClientDocuments = async () => {
        await readOneClient({
            variables: {
                filter: {
                    id: { eq: +(localStorage.getItem('clientId') ?? 0) },
                },
            },
        })
    }

    const loadVesselDocuments = async () => {
        await queryVesselListDocument({
            variables: {
                filter: { archived: { eq: false } },
            },
        })
    }
    const loadInventoryDocuments = async () => {
        await queryInventoriesListDocument()
    }
    const loadMaintenanceDocuments = async () => {
        await queryMaintenanceListDocument()
    }
    useEffect(() => {
        if (isLoading) {
            loadVesselDocuments()
            loadInventoryDocuments()
            loadMaintenanceDocuments()
            loadClientDocuments()
            setIsLoading(false)
        }
    }, [isLoading])

    const columns = createColumns([
        {
            accessorKey: 'name',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Name" />
            ),
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const document: any = row.original
                return (
                    <div className="space-y-1 py-2.5">
                        <div className="flex items-center">
                            <a
                                href={
                                    process.env.FILE_BASE_URL +
                                    document.fileFilename
                                }
                                target="_blank"
                                rel="noopener noreferrer"
                                className="font-medium hover:underline">
                                <p className=" break-all leading-tight">
                                    <span className="inline">
                                        {document.name}
                                    </span>
                                    <ExternalLink className="size-4 inline align-text-top ml-2" />
                                </p>
                            </a>
                        </div>
                        {document.type && document.type_title && (
                            <div className="flex items-center tablet-lg:hidden">
                                {/* <P className="text-xs">{document.type}:</P> */}
                                {document.type_title && (
                                    <span className="text-curious-blue-400 uppercase text-[10px]">
                                        {document.type_title}
                                    </span>
                                )}
                            </div>
                        )}
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.name || ''
                const valueB = rowB?.original?.name || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'type',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Module" />
            ),
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const document = row.original
                return <>{document.type}</>
            },
            breakpoint: 'tablet-lg',
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.type || ''
                const valueB = rowB?.original?.type || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'type_title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Item" />
            ),
            cellAlignment: 'left',
            breakpoint: 'tablet-lg',
            cell: ({ row }: { row: any }) => {
                const document = row.original
                return (
                    <>
                        {!isEmpty(document.type_title)
                            ? document.type_title
                            : '-'}
                    </>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.type_title || ''
                const valueB = rowB?.original?.type_title || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'created',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Upload date" />
            ),
            cellAlignment: 'right',
            breakpoint: 'tablet-sm',
            cell: ({ row }: { row: any }) => {
                const document = row.original
                return (
                    <>
                        {document?.created
                            ? formatDate(document.created)
                            : 'No Date'}
                    </>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const dateA = new Date(rowA?.original?.created || 0).getTime()
                const dateB = new Date(rowB?.original?.created || 0).getTime()
                return dateB - dateA
            },
        },
    ])

    return (
        <>
            <ListHeader
                icon={
                    <SealogsDocumentLockerIcon className="size-12" />
                }
                title="Document locker"
            />
            <div className="mt-16">
                {isLoading ? (
                    <Loading />
                ) : (
                    <DataTable
                        columns={columns}
                        data={vesselListWithDocuments}
                        showToolbar={true}
                        pageSize={20}
                        onChange={handleFilterOnChange}
                    />
                )}
            </div>
        </>
    )
}
