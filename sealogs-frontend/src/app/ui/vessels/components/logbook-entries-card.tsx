'use client'
import React, { useState, useRef } from 'react'
import Link from 'next/link'
import { SealogsLogbookIcon } from '../../../lib/icons/SealogsLogbookIcon'
import { formatDate } from '@/app/helpers/dateHelper'
import { SealogsTasks } from '@/app/lib/icons/SealogsTasks'
import { DataTable, ExtendedColumnDef } from '@/components/filteredTable' // adjust the path as needed
import { H1, H2 } from '@/components/ui/typography'
import { useIsMobile } from '@/components/hooks/use-mobile'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui'

export default function LogBookEntriesCard({
    vesselId,
    logbooks,
    imCrew,
    handleCreateNewLogEntry,
    isNewLogEntryDisabled,
    setVesselTab,
    vesselTitle,
    scrollToTabs,
}: {
    vesselId: number
    logbooks: any
    imCrew: any
    handleCreateNewLogEntry: any
    isNewLogEntryDisabled: any
    setVesselTab: (tab: string) => void
    vesselTitle: any
    scrollToTabs?: () => void
}) {
    const isMobile = useIsMobile()
    const initialPageSize = 5 // Initial number of pages to show
    const incrementPageSize = 20 // Number of pages to add when "View More" is clicked
    const [visiblePages, setVisiblePages] = useState(initialPageSize)
    const cardRef = useRef<HTMLDivElement>(null)

    // Calculate how many entries to show based on visible pages
    const entriesToShow = visiblePages
    const logbooksToDisplay = logbooks?.slice(0, entriesToShow) || []
    const hasMoreEntries = logbooks && logbooks.length > entriesToShow

    const columns = [
        {
            accessorKey: 'title',
            header: '',
            cell: ({ row }: { row: any }) => {
                const entry = row.original
                return (
                    <div className="flex justify-between items-center">
                        <Link
                            href={`/log-entries?vesselID=${vesselId}&logentryID=${entry.id}`}
                            className={`${entry.state === 'Locked' ? '' : ''} hover:text-primary`}>
                            {entry.state === 'Locked' ? (
                                'Log entry'
                            ) : (
                                <strong>Open log: </strong>
                            )}{' '}
                            {entry?.startDate
                                ? formatDate(entry.startDate)
                                : ''}
                        </Link>
                    </div>
                )
            },
        },
        {
            accessorKey: 'due',
            header: '',
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const entry = row.original
                return (
                    <div className="flex justify-end">
                        <div
                            className={` w-fit ${
                                entry.state === 'Locked' ? 'p-2' : 'alert'
                            } whitespace-nowrap`}>
                            {entry.state}
                        </div>
                    </div>
                )
            },
        },
    ]

    return (
        <Card ref={cardRef}>
            <div className="flex p-3 phablet:py-3 phablet:px-0 items-baseline gap-2 phablet:gap-4">
                <SealogsLogbookIcon className='size-12' />
                <Link href={`/vessel`}>
                    <H1>{vesselTitle}</H1>
                </Link>
            </div>
            {logbooks.length > 0 ? (
                <>
                    <div className="p-0 phablet:pt-1">
                        <DataTable
                            columns={
                                columns as ExtendedColumnDef<any, unknown>[]
                            }
                            data={logbooksToDisplay}
                            showToolbar={false}
                            pageSize={
                                isMobile
                                    ? logbooksToDisplay.length
                                    : initialPageSize
                            }
                            className="border-0 shadow-none"
                        />
                    </div>
                </>
            ) : (
                <div className="flex justify-between items-center gap-2 p-2 pt-4">
                    <div>
                        <SealogsTasks />
                    </div>
                    <p>
                        {!imCrew && (
                            <Button
                                onClick={handleCreateNewLogEntry}
                                disabled={isNewLogEntryDisabled}>
                                Create a log entry
                            </Button>
                        )}
                    </p>
                </div>
            )}
            {logbooks.length > initialPageSize && (
                <div className="mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-accent border border-curious-blue-100 p-5 text-center">
                    {isMobile && hasMoreEntries ? (
                        <Button
                            variant="outline"
                            onClick={() =>
                                setVisiblePages(
                                    (prev) => prev + incrementPageSize,
                                )
                            }
                            className="text-accent-foreground uppercase hover:text-primary text-xs bg-transparent border-none shadow-none p-0 h-auto">
                            View More
                            <span className="hidden md:inline-block">
                                &nbsp;log entries&nbsp;
                            </span>
                        </Button>
                    ) : isMobile &&
                      !hasMoreEntries &&
                      logbooks.length > initialPageSize ? (
                        <Button
                            variant="outline"
                            onClick={() => {
                                setVisiblePages(initialPageSize)
                                cardRef.current?.scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'center',
                                })
                            }}
                            className="text-accent-foreground uppercase hover:text-primary text-xs bg-transparent border-none shadow-none p-0 h-auto">
                            View Less
                            <span className="hidden md:inline-block">
                                &nbsp;log entries&nbsp;
                            </span>
                        </Button>
                    ) : (
                        <Link
                            href="#"
                            onClick={(e) => {
                                e.preventDefault()
                                setVesselTab('logEntries')
                                scrollToTabs?.()
                            }}
                            className="hidden md:block text-accent-foreground uppercase hover:text-primary text-xs">
                            View all
                            <span className="hidden md:inline-block">
                                &nbsp;log entries&nbsp;
                            </span>
                        </Link>
                    )}
                </div>
            )}
        </Card>
    )
}
