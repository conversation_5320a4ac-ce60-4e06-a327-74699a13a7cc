'use client'
import { SealogsMaintenanceIcon } from '../../../lib/icons/SealogsMaintenanceIcon'
import { SealogsMaintenanceEmptyStateIcon } from '../../../lib/icons/SealogsMaintenanceEmptyStateIcon'
import Link from 'next/link'
import { DataTable, ExtendedColumnDef } from '@/components/filteredTable'
import { useSearchParams } from 'next/navigation'
import { H1, H2, P } from '@/components/ui/typography'
import { useIsMobile } from '@/components/hooks/use-mobile'
import { Button } from '@/components/ui/button'
import { useState, useRef } from 'react'
import {
    Avatar,
    AvatarFallback,
    Card,
    getCrewInitials,
    Tooltip,
    TooltipContent,
    TooltipTrigger,
} from '@/components/ui'
import { SquareArrowOutUpRight } from 'lucide-react'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'
import { StatusBadge } from '../../maintenance/list/list'

// Define the task type to fix TypeScript errors
interface MaintenanceTask {
    id: string
    name: string
    severity: string
    status: string
    assignedToID?: string
    assignedTo?: {
        id: string
        name: string
    }
    isOverDue?: {
        status: string
        days: string
    }
}

export default function MaintenanceCard({
    maintenanceTasks,
    pathname,
    setVesselTab,
    scrollToTabs,
}: {
    maintenanceTasks: MaintenanceTask[]
    pathname: string
    setVesselTab: (tab: string) => void
    scrollToTabs?: () => void
}) {
    const searchParams = useSearchParams()
    const isMobile = useIsMobile()
    const initialPageSize = 5 // Initial number of tasks to show
    const incrementPageSize = 40 // Number of tasks to add when "View More" is clicked
    const [visiblePages, setVisiblePages] = useState(initialPageSize)
    const bp = useBreakpoints()
    const cardRef = useRef<HTMLDivElement>(null)

    // Filter out completed and draft tasks
    const activeTasks =
        maintenanceTasks?.filter(
            (task: any) =>
                !(
                    task.status === 'Completed' ||
                    task.status === 'Save_As_Draft'
                ),
        ) || []

    // Calculate how many tasks to show based on visible pages
    const entriesToShow = visiblePages
    const tasksToDisplay = isMobile
        ? activeTasks.slice(0, entriesToShow)
        : activeTasks.slice(0, 5)
    const hasMoreTasks = activeTasks && activeTasks.length > entriesToShow

    const columns = [
        {
            accessorKey: 'title',
            header: '',
            cell: ({ row }: { row: any }) => {
                const task = row.original
                return (
                    <div className="flex items-center h-full">
                        <Link
                            href={`/maintenance?taskID=${task.id}&redirect_to=${pathname}?${searchParams.toString()}`}
                            className={`${task.severity === 'High' ? 'group-hover:text-destructive' : ''} hover:text-primary`}>
                            {task.name}
                        </Link>
                    </div>
                )
            },
        },
        {
            accessorKey: 'assignedToID',
            header: '',
            breakpoint: 'standard',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const task = row.original as MaintenanceTask

                // Check if task has assignedTo information
                if (
                    !task.assignedTo ||
                    !task.assignedTo.id ||
                    task.assignedTo.id === '0'
                ) {
                    return <div className="w-6 text-center lg:hidden">-</div>
                }

                const assignedPerson = {
                    id: task.assignedTo.id,
                    name: task.assignedTo.name,
                    firstName: task.assignedTo.name.split(' ')[0] || '',
                    surname:
                        task.assignedTo.name.split(' ').slice(1).join(' ') ||
                        '',
                }

                const fullName = task.assignedTo.name

                return (
                    <div className="flex items-center lg:hidden gap-2.5">
                        <Tooltip>
                            <TooltipTrigger>
                                <Avatar size="sm" variant={'secondary'}>
                                    <AvatarFallback className="text-sm">
                                        {getCrewInitials(
                                            assignedPerson.firstName,
                                            assignedPerson.surname,
                                        )}
                                    </AvatarFallback>
                                </Avatar>
                            </TooltipTrigger>
                            <TooltipContent>
                                <Link
                                    href={`/crew/info?id=${assignedPerson.id}`}
                                    className="hover:underline flex items-center gap-2">
                                    {fullName}{' '}
                                    <SquareArrowOutUpRight size={16} />
                                </Link>
                            </TooltipContent>
                        </Tooltip>
                        <Link
                            href={`/crew/info?id=${task.assignedTo.id}`}
                            className="hover:underline hidden tablet-md:block">
                            {task.assignedTo.name}
                        </Link>
                    </div>
                )
            },
        },
        {
            accessorKey: 'due',
            header: '',
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const task = row?.original
                if (!task) {
                    return <div>-</div>
                }
                const overDueStatus = task.isOverDue?.status
                const overDueDays = task.isOverDue?.day
                const isEngineHour =
                    task.isOverDue?.days.includes('engine hour')
                return (
                    <>
                        {isEngineHour ? (
                            <>
                                {overDueDays < 0 ? (
                                    <div className="flex-1 flex justify-end">
                                        <div className="items-end w-fit alert whitespace-nowrap">
                                            {overDueDays * -1 + ' hours ago'}
                                        </div>
                                    </div>
                                ) : (
                                    <div className="flex-1 flex justify-end">
                                        <div className="items-end w-fit whitespace-nowrap">
                                            {overDueDays * 1 + ' hours'}
                                        </div>
                                    </div>
                                )}
                            </>
                        ) : (
                            <>
                                {overDueStatus === 'High' ? (
                                    <div className="flex-1 flex justify-end">
                                        <div
                                            className={` items-end w-fit
                            ${overDueStatus === 'High' ? 'alert whitespace-nowrap' : ''}
                            `}>
                                            {overDueDays * -1 + ' days ago'}
                                        </div>
                                    </div>
                                ) : (
                                    <StatusBadge maintenanceCheck={task} />
                                )}
                            </>
                        )}
                    </>
                )
            },
        },
    ]

    return (
        <Card ref={cardRef}>
            <div className="flex py-3 items-baseline gap-2 phablet:gap-4">
                <SealogsMaintenanceIcon className={`size-12 shrink-0`} />
                <Link onClick={() => setVesselTab('maintenance')} href={''}>
                    <H1>Maintenance</H1>
                </Link>
            </div>

            {activeTasks.length > 0 ? (
                <>
                    <div className="pt-0 phablet:pt-1">
                        <DataTable
                            columns={
                                columns as ExtendedColumnDef<any, unknown>[]
                            }
                            data={tasksToDisplay}
                            showToolbar={false}
                            pageSize={
                                isMobile
                                    ? tasksToDisplay.length
                                    : initialPageSize
                            }
                            className="border-0 shadow-none"
                        />
                    </div>
                </>
            ) : (
                <div className="flex justify-between items-center gap-2 p-2 pt-4">
                    <div>
                        <SealogsMaintenanceEmptyStateIcon />
                    </div>
                    <p>
                        Holy mackerel! You are up to date with all your
                        maintenance. Only thing left to do is, to go fishing
                    </p>
                </div>
            )}

            {activeTasks.length > initialPageSize && (
                <div className="mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-accent border border-curious-blue-100 p-5 text-center">
                    {isMobile && hasMoreTasks ? (
                        <Button
                            variant="outline"
                            onClick={() =>
                                setVisiblePages(
                                    (prev) => prev + incrementPageSize,
                                )
                            }
                            className="text-accent-foreground uppercase hover:text-primary text-xs bg-transparent border-none shadow-none p-0 h-auto">
                            View More
                            <span className="hidden md:inline-block">
                                &nbsp;maintenance tasks&nbsp;
                            </span>
                        </Button>
                    ) : isMobile &&
                      !hasMoreTasks &&
                      activeTasks.length > initialPageSize ? (
                        <Button
                            variant="outline"
                            onClick={() => {
                                setVisiblePages(initialPageSize)
                                cardRef.current?.scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'start',
                                })
                            }}
                            className="text-accent-foreground uppercase hover:text-primary text-xs bg-transparent border-none shadow-none p-0 h-auto">
                            View Less
                            <span className="hidden md:inline-block">
                                &nbsp;maintenance tasks&nbsp;
                            </span>
                        </Button>
                    ) : (
                        <Link
                            href="#"
                            onClick={(e) => {
                                e.preventDefault()
                                setVesselTab('maintenance')
                                scrollToTabs?.()
                            }}
                            className="hidden md:block text-accent-foreground uppercase hover:text-primary text-xs">
                            View all
                            <span className="hidden md:inline-block">
                                &nbsp;maintenance tasks&nbsp;
                            </span>
                        </Link>
                    )}
                </div>
            )}
        </Card>
    )
}
