'use client'
import React, { useState, useRef } from 'react'
import Link from 'next/link'
import { SealogsInventoryIcon } from '../../../lib/icons/SealogsInventoryIcon'
import { DataTable, ExtendedColumnDef } from '@/components/filteredTable'
import { H1, P } from '@/components/ui/typography'
import { useIsMobile } from '@/components/hooks/use-mobile'
import { Button } from '@/components/ui/button'
import { Check, Package } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { useSearchParams } from 'next/navigation'
import { isOverDueTask } from '@/app/lib/actions'
import { Card, Popover, PopoverContent, PopoverTrigger } from '@/components/ui'

interface InventoryItem {
    id: number
    item: string
    quantity: number
    location?: string
    maintenanceStatus?: string
    vessel?: {
        title: string
    }
    categories?: {
        nodes: Array<{
            id: number
            name: string
        }>
    }
}

export default function InventoryCard({
    inventories,
    setVesselTab,
    vesselId,
    pathname,
}: {
    inventories: InventoryItem[]
    setVesselTab: (tab: string) => void
    vesselId: number
    pathname: string
}) {
    const isMobile = useIsMobile()
    const searchParams = useSearchParams()
    const initialPageSize = 5 // Initial number of items to show
    const incrementPageSize = 20 // Number of items to add when "View More" is clicked
    const [visiblePages, setVisiblePages] = useState(initialPageSize)
    const [isExpanded, setIsExpanded] = useState(false)
    const cardRef = useRef<HTMLDivElement>(null)

    // Calculate how many items to show based on visible pages
    const entriesToShow = visiblePages
    const inventoriesToDisplay = isMobile
        ? inventories?.slice(0, entriesToShow) || []
        : inventories?.slice(0, 5) || []
    const hasMoreInventories = inventories && inventories.length > entriesToShow
    const canShowMore = inventories && inventories.length > 5

    const columns = [
        {
            accessorKey: 'item',
            header: '',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return (
                    <div className="flex items-center h-full">
                        <Link
                            href={`/inventory/view/?id=${inventory.id}&redirect_to=${pathname}?${searchParams.toString()}&tab=inventory`}
                            className="hover:text-primary">
                            {inventory.quantity} x {inventory.item}
                        </Link>
                    </div>
                )
            },
        },
        {
            accessorKey: 'categories',
            header: '',
            cellAlignment: 'left',
            breakpoint: 'tablet-sm',
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return (
                    <div className="flex gap-2 items-center">
                        {inventory.categories?.nodes
                            ?.slice(0, 2)
                            .map((cat: any, idx: number) => (
                                <Badge
                                    key={String(idx)}
                                    type="normal"
                                    variant="outline"
                                    className="font-normal">
                                    {cat.name}
                                </Badge>
                            ))}
                        {inventory.categories?.nodes?.length > 2 && (
                            <Popover>
                                <PopoverTrigger asChild>
                                    <Button
                                        variant="outline"
                                        className="!p-2 bg-transparent">
                                        +{' '}
                                        {inventory.categories.nodes.length - 2}{' '}
                                        more
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-80">
                                    <div className="space-y-2">
                                        <P className="font-medium text-sm">
                                            All Categories
                                        </P>
                                        <div className="flex flex-wrap gap-2">
                                            {inventory.categories.nodes.map(
                                                (cat: any, idx: number) => (
                                                    <Badge
                                                        key={String(idx)}
                                                        type="normal"
                                                        variant="outline"
                                                        className="font-normal">
                                                        {cat.name}
                                                    </Badge>
                                                ),
                                            )}
                                        </div>
                                    </div>
                                </PopoverContent>
                            </Popover>
                        )}
                    </div>
                )
            },
        },
        {
            accessorKey: 'maintenance',
            header: '',
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const inventory = row.original

                // Calculate maintenance status from componentMaintenanceChecks
                const getMaintenanceStatus = (inventory: any) => {
                    const checks =
                        inventory.componentMaintenanceChecks?.nodes || []

                    if (checks.length === 0) {
                        return null
                    }

                    // Filter active tasks (not archived)
                    const activeTasks = checks.filter(
                        (task: any) => !task?.archived,
                    )

                    // Count overdue tasks using the same logic as inventory view
                    const overdueTasks = activeTasks.filter((task: any) => {
                        const overDueInfo = isOverDueTask(task)
                        const isOverdue =
                            task.status !== 'Completed' &&
                            task.status !== 'Save_As_Draft' &&
                            overDueInfo.status !== 'Completed' &&
                            overDueInfo.status !== 'Upcoming'

                        return isOverdue
                    })

                    if (overdueTasks.length > 0) {
                        return { type: 'overdue', count: overdueTasks.length }
                    }

                    // If there are maintenance checks but none are overdue, show good status
                    return { type: 'good' }
                }

                const maintenanceStatus = getMaintenanceStatus(inventory)

                if (!maintenanceStatus) return null

                return (
                    <div className="flex justify-end">
                        <Badge
                            variant={
                                maintenanceStatus.type === 'overdue'
                                    ? 'destructive'
                                    : maintenanceStatus.type === 'good'
                                      ? 'success'
                                      : 'secondary'
                            }>
                            {maintenanceStatus.type === 'overdue' ? (
                                maintenanceStatus.count
                            ) : (
                                <Check className="h-5 w-5" />
                            )}
                        </Badge>
                    </div>
                )
            },
        },
    ]

    return (
        <Card ref={cardRef}>
            <div className="lg:flex flex-col lg:justify-between">
                <div className="flex py-3 items-baseline gap-2 phablet:gap-4">
                    <SealogsInventoryIcon className="size-12" />
                    <Link onClick={() => setVesselTab('inventory')} href={''}>
                        <H1>Inventory</H1>
                    </Link>
                </div>

                {inventories && inventories.length > 0 ? (
                    <>
                        <div className="pt-0 phablet:pt-1">
                            <DataTable
                                columns={
                                    columns as ExtendedColumnDef<any, unknown>[]
                                }
                                data={inventoriesToDisplay}
                                showToolbar={false}
                                pageSize={
                                    isMobile
                                        ? inventoriesToDisplay.length
                                        : initialPageSize
                                }
                                className="border-0 shadow-none"
                            />
                        </div>
                        {isMobile && canShowMore && (
                            <div className="mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-accent border border-curious-blue-100 p-5 text-center">
                                <Button
                                    variant="outline"
                                    className="text-accent-foreground uppercase hover:text-primary text-xs bg-transparent border-none shadow-none p-0 h-auto"
                                    onClick={() => {
                                        if (hasMoreInventories) {
                                            setVisiblePages(
                                                visiblePages +
                                                    incrementPageSize,
                                            )
                                            setIsExpanded(true)
                                        } else {
                                            setVisiblePages(initialPageSize)
                                            setIsExpanded(false)
                                            cardRef.current?.scrollIntoView({
                                                behavior: 'smooth',
                                                block: 'start',
                                            })
                                        }
                                    }}>
                                    {hasMoreInventories
                                        ? 'View More'
                                        : 'View Less'}
                                </Button>
                            </div>
                        )}
                    </>
                ) : (
                    <div className="flex justify-between items-center min-h-40 gap-2 p-2 pt-4">
                        <div className="text-center">
                            <p className="text-muted-foreground">
                                No inventory items
                            </p>
                        </div>
                    </div>
                )}
            </div>
        </Card>
    )
}
