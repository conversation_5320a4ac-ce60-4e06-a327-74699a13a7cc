'use client'

import { useTheme } from "next-themes";

const React = require('react')

export function SealogsMaintenanceIcon(props: any) {
  const { theme, setTheme } = useTheme()

  const getTheme = () => {
    switch (theme) {
      case 'dark':
        return 'Dark'
      case 'light':
        return 'Light'
      default:
        return 'System'
    }
  }

  getTheme()

  if (theme === 'light') {
    return React.createElement('img', {
      src: '/sealogs-V2_Icons/125px_icon/maintenance-light.svg',
      alt: 'Crew Light Icon',
      ...props,
    });
  } else if (theme === 'dark') {
    return React.createElement('img', {
      src: '/sealogs-V2_Icons/125px_icon/maintenance-dark.svg',
      alt: 'Crew Dark Icon',
      ...props,
    });
  }
}
