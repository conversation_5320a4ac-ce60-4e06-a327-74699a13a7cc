import * as React from 'react'

import {
    <PERSON><PERSON>,
    <PERSON>bar<PERSON>ontent,
    <PERSON>bar<PERSON>ooter,
    SidebarHeader,
    SidebarRail,
} from '@/components/ui/sidebar'
import { Team } from '@/components/team'

import { useEffect, useState } from 'react'
import { NavMain } from './nav-main'
import { NavUser } from './nav-user'
import { NavSingle } from './nav-single-links'
import {
    SealogsCrewIcon,
    SealogsDashboardIcon,
    SealogsHealthSafetyIcon,
    SealogsInventoryIcon,
    SealogsVesselsIcon,
} from '@/app/lib/icons'
import { useTheme } from 'next-themes'
import { SealogsVesselsSelectedIcon } from '@/app/lib/icons/SealogsVesselsSelectedIcon'
import { usePathname } from 'next/navigation'
import { SealogsCrewSelectedIcon } from '@/app/lib/icons/SealogsCrewSelectedIcon'
import { SealogsHealthSafetySelectedIcon } from '@/app/lib/icons/SealogsHealthSafetySelectedIcon'
import { SealogsInventorySelectedIcon } from '@/app/lib/icons/SealogsInventorySelectedIcon'
import { SealogsDashboardSelectedIcon } from '@/app/lib/icons/SealogsDashboardSelectedIcon'

//logo versions: logo-small-dark.png | logo-small.png

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
    const [clientTitle, setClientTitle] = useState('')
    const [isLoading, setIsLoading] = useState(true)
    const [loggedUserName, setLoggedUserName] = useState('')
    const [loggedEmail, setLoggedEmail] = useState('')
    const pathname = usePathname()

    const NavData = {
        user: {
            name: loggedUserName,
            email: loggedEmail,
        },
        team: {
            name: 'South Inc',
            logo: 'logo-small.png',
            plan: 'powered by SeaLogs',
        },
        versions: ['3.4.0', '1.1.0-alpha', '2.0.0-beta1'],
        singleLinks: [
            {
                name: 'Home port',
                url: '/dashboard',
                icon:
                    pathname === '/dashboard' ? (
                        <SealogsDashboardSelectedIcon className="size-6" />
                    ) : (
                        <SealogsDashboardIcon className="size-6" />
                    ),
            },
            {
                name: 'All vessels',
                url: '/vessel',
                icon:
                    pathname === '/vessel' ? (
                        <SealogsVesselsSelectedIcon className="size-6" />
                    ) : (
                        <SealogsVesselsIcon className="size-6" />
                    ),
            },
        ],
        navMain: [
            {
                title: 'Crew',
                url: '#',
                icon:
                    pathname === '/crew' || pathname === '/crew-training' ? (
                        <SealogsCrewSelectedIcon className="size-6" />
                    ) : (
                        <SealogsCrewIcon className="size-6" />
                    ),
                items: [
                    {
                        title: 'All crew',
                        url: '/crew',
                    },
                    {
                        title: 'Training / Drills',
                        url: '/crew-training',
                    },
                ],
            },
            {
                title: 'Health & safety',
                url: '#',
                icon:
                    pathname === '/risk-evaluations' ||
                    pathname === '/risk-strategies' ||
                    pathname === '/training-matrix' ? (
                        <SealogsHealthSafetySelectedIcon className="size-6" />
                    ) : (
                        <SealogsHealthSafetyIcon className="size-6" />
                    ),
                items: [
                    // {
                    //     title: 'Risk Evaluations',
                    //     url: '/risk-evaluations',
                    // },
                    // {
                    //     title: 'Risk Strategies',
                    //     url: '/risk-strategies',
                    // },
                    {
                        title: 'Drills / training matrix',
                        url: '/training-matrix',
                    },
                ],
            },
        ],
        navMain2: [
            {
                title: 'Inventory',
                url: '#',
                icon:
                    pathname === '/inventory' ||
                    pathname === '/inventory/suppliers' ||
                    pathname === '/maintenance' ||
                    pathname === '/document-locker' ? (
                        <SealogsInventorySelectedIcon className="size-6" />
                    ) : (
                        <SealogsInventoryIcon className="size-6" />
                    ),
                items: [
                    {
                        title: 'All inventory',
                        url: '/inventory',
                    },
                    {
                        title: 'Suppliers',
                        url: '/inventory/suppliers',
                    },
                    {
                        title: 'Maintenance',
                        url: '/maintenance',
                    },
                    {
                        title: 'Documents',
                        url: '/document-locker',
                    },
                ],
            },
        ],
    }

    useEffect(() => {
        if (isLoading) {
            setClientTitle(localStorage.getItem('clientTitle') || '')
            // init()
            setIsLoading(false)
        }
        if (typeof window !== 'undefined') {
            setLoggedUserName(
                `${localStorage.getItem('firstName') ?? ''} ${localStorage.getItem('surname') ?? ''}`,
            )
            const user = localStorage.getItem('user')
            if (user !== null) {
                setLoggedEmail(`${JSON.parse(user).email ?? ''}`)
            }
        }
    }, [isLoading])

    // Memoize the team data to prevent unnecessary re-renders
    const teamData = React.useMemo(
        () => ({
            ...NavData.team,
            name: clientTitle || NavData.team.name,
        }),
        [clientTitle],
    )

    // Memoize the user data to prevent unnecessary re-renders
    const userData = React.useMemo(
        () => ({
            ...NavData.user,
            name: loggedUserName || NavData.user.name,
            email: loggedEmail || NavData.user.email,
        }),
        [loggedUserName, loggedEmail],
    )
    return (
        <Sidebar collapsible="icon" {...props}>
            <SidebarHeader>
                <Team team={teamData} />
            </SidebarHeader>
            <SidebarContent>
                <NavSingle projects={NavData.singleLinks} />
                <NavMain items={NavData.navMain} />
                <NavMain items={NavData.navMain2} />
            </SidebarContent>
            <SidebarFooter className="pb-6">
                <NavUser user={userData} />
            </SidebarFooter>
            <SidebarRail />
        </Sidebar>
    )
}
